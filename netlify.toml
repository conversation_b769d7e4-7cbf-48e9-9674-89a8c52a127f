[build]
  base = "frontend"
  publish = "frontend/out"
  command = "npm run build:netlify"

[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "9"
  BUILD_TARGET = "netlify"

# Redirect rules for SPA
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"

[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.woff2"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Serverless functions
[functions]
  directory = "backend/netlify/functions"
  node_bundler = "esbuild"

# Edge functions for geo-location
[edge_functions]
  directory = "netlify/edge-functions"

# Form handling
[forms]
  settings = true

# Large Media for video files
[large_media]
  settings = true

# Build plugins
[[plugins]]
  package = "@netlify/plugin-nextjs"

[[plugins]]
  package = "netlify-plugin-cache"
  [plugins.inputs]
    paths = ["frontend/node_modules", "backend/node_modules"]

# Environment variables (set in Netlify dashboard)
# NEXT_PUBLIC_API_URL
# DATABASE_URL
# REDIS_URL
# OPENAI_API_KEY
# ELEVENLABS_API_KEY
# AWS_ACCESS_KEY_ID
# AWS_SECRET_ACCESS_KEY
# JWT_SECRET
