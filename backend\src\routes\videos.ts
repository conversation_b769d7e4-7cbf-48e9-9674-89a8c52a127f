import express from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { videoRateLimiter } from '../middleware/rateLimiter';
import { requireSubscription } from '../middleware/auth';
import { VideoGenerationService } from '../services/videoGeneration';
import { logger } from '../utils/logger';

const router = express.Router();
const prisma = new PrismaClient();
const videoService = new VideoGenerationService();

// Validation middleware
const validateVideoGeneration = [
  body('templateId').isString().notEmpty().withMessage('Template ID is required'),
  body('niche').isString().notEmpty().withMessage('Niche is required'),
  body('topic').optional().isString(),
  body('duration').isInt({ min: 5, max: 180 }).withMessage('Duration must be between 5 and 180 seconds'),
  body('voiceId').isString().notEmpty().withMessage('Voice ID is required'),
  body('voiceSettings').isObject().withMessage('Voice settings must be an object'),
  body('voiceSettings.speed').isFloat({ min: 0.5, max: 2.0 }).withMessage('Voice speed must be between 0.5 and 2.0'),
  body('voiceSettings.pitch').isFloat({ min: -1.0, max: 1.0 }).withMessage('Voice pitch must be between -1.0 and 1.0'),
  body('voiceSettings.emotion').isIn(['neutral', 'excited', 'serious', 'friendly']).withMessage('Invalid emotion'),
  body('platforms').isArray({ min: 1 }).withMessage('At least one platform is required'),
  body('platforms.*').isIn(['tiktok', 'youtube', 'instagram', 'twitter']).withMessage('Invalid platform'),
  body('customization').optional().isObject(),
];

const validateVideoUpdate = [
  param('id').isString().notEmpty().withMessage('Video ID is required'),
  body('title').optional().isString().trim().isLength({ min: 1, max: 200 }),
  body('description').optional().isString().trim().isLength({ max: 1000 }),
  body('published').optional().isBoolean(),
];

// GET /api/videos - Get user's videos
router.get('/', 
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
  query('status').optional().isIn(['PROCESSING', 'COMPLETED', 'FAILED', 'PUBLISHED']),
  query('published').optional().isBoolean().toBoolean(),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation Error',
          details: errors.array(),
        });
      }

      const page = req.query.page as number || 1;
      const limit = req.query.limit as number || 20;
      const status = req.query.status as string;
      const published = req.query.published as boolean;
      const offset = (page - 1) * limit;

      const where: any = {
        userId: req.user!.id,
      };

      if (status) {
        where.status = status;
      }

      if (published !== undefined) {
        where.published = published;
      }

      const [videos, total] = await Promise.all([
        prisma.video.findMany({
          where,
          include: {
            analytics: {
              select: {
                views: true,
                likes: true,
                shares: true,
                comments: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          skip: offset,
          take: limit,
        }),
        prisma.video.count({ where }),
      ]);

      // Aggregate analytics for each video
      const videosWithAnalytics = videos.map(video => ({
        ...video,
        analytics: video.analytics.length > 0 ? {
          views: video.analytics.reduce((sum, a) => sum + a.views, 0),
          likes: video.analytics.reduce((sum, a) => sum + a.likes, 0),
          shares: video.analytics.reduce((sum, a) => sum + a.shares, 0),
          comments: video.analytics.reduce((sum, a) => sum + a.comments, 0),
        } : {
          views: 0,
          likes: 0,
          shares: 0,
          comments: 0,
        },
      }));

      res.json({
        videos: videosWithAnalytics,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      });

    } catch (error) {
      logger.error('Error fetching videos:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to fetch videos',
      });
    }
  }
);

// POST /api/videos/generate - Generate new video
router.post('/generate',
  videoRateLimiter,
  validateVideoGeneration,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation Error',
          details: errors.array(),
        });
      }

      const userId = req.user!.id;
      const user = req.user!;

      // Check subscription limits
      if (user.subscriptionTier === 'FREE') {
        const monthlyUsage = await prisma.video.count({
          where: {
            userId,
            createdAt: {
              gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
            },
          },
        });

        if (monthlyUsage >= 5) {
          return res.status(403).json({
            error: 'Subscription Limit Exceeded',
            message: 'Free tier allows 5 videos per month. Please upgrade your plan.',
            upgradeUrl: '/pricing',
          });
        }
      }

      // Create project if not provided
      let projectId = req.body.projectId;
      if (!projectId) {
        const project = await prisma.project.create({
          data: {
            name: `Project ${new Date().toISOString()}`,
            templateId: req.body.templateId,
            niche: req.body.niche,
            settings: req.body,
            userId,
          },
        });
        projectId = project.id;
      }

      // Start video generation
      const result = await videoService.generateVideo(userId, projectId, req.body);

      // Update user's monthly usage
      await prisma.user.update({
        where: { id: userId },
        data: {
          videosGenerated: { increment: 1 },
          monthlyUsage: { increment: 1 },
        },
      });

      res.status(202).json({
        message: 'Video generation started',
        videoId: result.videoId,
        status: result.status,
        estimatedTime: '2-5 minutes',
      });

    } catch (error) {
      logger.error('Error generating video:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to start video generation',
      });
    }
  }
);

// GET /api/videos/:id - Get specific video
router.get('/:id',
  param('id').isString().notEmpty(),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation Error',
          details: errors.array(),
        });
      }

      const video = await prisma.video.findFirst({
        where: {
          id: req.params.id,
          userId: req.user!.id,
        },
        include: {
          project: true,
          analytics: true,
        },
      });

      if (!video) {
        return res.status(404).json({
          error: 'Not Found',
          message: 'Video not found',
        });
      }

      res.json(video);

    } catch (error) {
      logger.error('Error fetching video:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to fetch video',
      });
    }
  }
);

// PUT /api/videos/:id - Update video
router.put('/:id',
  validateVideoUpdate,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation Error',
          details: errors.array(),
        });
      }

      const video = await prisma.video.findFirst({
        where: {
          id: req.params.id,
          userId: req.user!.id,
        },
      });

      if (!video) {
        return res.status(404).json({
          error: 'Not Found',
          message: 'Video not found',
        });
      }

      const updatedVideo = await prisma.video.update({
        where: { id: req.params.id },
        data: {
          title: req.body.title,
          description: req.body.description,
          published: req.body.published,
          publishedAt: req.body.published ? new Date() : null,
        },
      });

      res.json(updatedVideo);

    } catch (error) {
      logger.error('Error updating video:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to update video',
      });
    }
  }
);

// DELETE /api/videos/:id - Delete video
router.delete('/:id',
  param('id').isString().notEmpty(),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation Error',
          details: errors.array(),
        });
      }

      const video = await prisma.video.findFirst({
        where: {
          id: req.params.id,
          userId: req.user!.id,
        },
      });

      if (!video) {
        return res.status(404).json({
          error: 'Not Found',
          message: 'Video not found',
        });
      }

      // Delete video and related data
      await prisma.video.delete({
        where: { id: req.params.id },
      });

      // TODO: Delete video files from storage

      res.json({
        message: 'Video deleted successfully',
      });

    } catch (error) {
      logger.error('Error deleting video:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to delete video',
      });
    }
  }
);

// POST /api/videos/:id/publish - Publish video to platforms
router.post('/:id/publish',
  param('id').isString().notEmpty(),
  body('platforms').isArray({ min: 1 }).withMessage('At least one platform is required'),
  body('platforms.*').isIn(['tiktok', 'youtube', 'instagram', 'twitter']),
  body('scheduledFor').optional().isISO8601().toDate(),
  requireSubscription('STARTER'),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation Error',
          details: errors.array(),
        });
      }

      const video = await prisma.video.findFirst({
        where: {
          id: req.params.id,
          userId: req.user!.id,
          status: 'COMPLETED',
        },
      });

      if (!video) {
        return res.status(404).json({
          error: 'Not Found',
          message: 'Video not found or not ready for publishing',
        });
      }

      // TODO: Implement platform publishing logic
      // This would integrate with TikTok, YouTube, Instagram, Twitter APIs

      await prisma.video.update({
        where: { id: req.params.id },
        data: {
          published: true,
          publishedAt: new Date(),
          scheduledFor: req.body.scheduledFor,
        },
      });

      res.json({
        message: 'Video publishing initiated',
        platforms: req.body.platforms,
      });

    } catch (error) {
      logger.error('Error publishing video:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to publish video',
      });
    }
  }
);

export default router;
