'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  PlusIcon, 
  PlayIcon, 
  EyeIcon, 
  HeartIcon, 
  ShareIcon,
  TrendingUpIcon,
  VideoIcon,
  ClockIcon,
  SparklesIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '@/hooks/useAuth';
import { useVideos } from '@/hooks/useVideos';
import { VideoCard } from '@/components/VideoCard';
import { StatsCard } from '@/components/StatsCard';
import { CreateVideoModal } from '@/components/CreateVideoModal';
import { UpgradePrompt } from '@/components/UpgradePrompt';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { EmptyState } from '@/components/EmptyState';

interface DashboardStats {
  totalVideos: number;
  totalViews: number;
  totalLikes: number;
  totalShares: number;
  monthlyUsage: number;
  usageLimit: number;
}

export default function DashboardPage() {
  const { user, isLoading: authLoading } = useAuth();
  const { videos, isLoading: videosLoading, refetch } = useVideos();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'published' | 'draft'>('all');

  // Fetch dashboard stats
  useEffect(() => {
    if (user) {
      fetchDashboardStats();
    }
  }, [user]);

  const fetchDashboardStats = async () => {
    try {
      const response = await fetch('/api/analytics/dashboard', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard stats:', error);
    }
  };

  // Filter videos based on selected filter
  const filteredVideos = videos?.filter(video => {
    if (selectedFilter === 'all') return true;
    if (selectedFilter === 'published') return video.published;
    if (selectedFilter === 'draft') return !video.published;
    return true;
  }) || [];

  // Check if user needs to upgrade
  const needsUpgrade = user?.subscriptionTier === 'FREE' && (stats?.monthlyUsage || 0) >= (stats?.usageLimit || 5);

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Welcome back, {user?.firstName || user?.username || 'Creator'}!
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Create viral content that gets millions of views
              </p>
            </div>
            
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setShowCreateModal(true)}
              disabled={needsUpgrade}
              className="inline-flex items-center px-6 py-3 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white font-medium rounded-lg shadow-sm transition-colors"
            >
              <PlusIcon className="w-5 h-5 mr-2" />
              Create Video
            </motion.button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Upgrade prompt for free users */}
        {needsUpgrade && <UpgradePrompt className="mb-8" />}

        {/* Stats Grid */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <StatsCard
              title="Total Videos"
              value={stats.totalVideos}
              icon={VideoIcon}
              trend={+12}
              color="blue"
            />
            <StatsCard
              title="Total Views"
              value={stats.totalViews}
              icon={EyeIcon}
              trend={+25}
              color="green"
              format="number"
            />
            <StatsCard
              title="Engagement"
              value={stats.totalLikes + stats.totalShares}
              icon={HeartIcon}
              trend={+18}
              color="pink"
              format="number"
            />
            <StatsCard
              title="Monthly Usage"
              value={`${stats.monthlyUsage}/${stats.usageLimit === -1 ? '∞' : stats.usageLimit}`}
              icon={SparklesIcon}
              trend={0}
              color="purple"
            />
          </div>
        )}

        {/* Video Filter Tabs */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex space-x-8 px-6" aria-label="Tabs">
              {[
                { key: 'all', label: 'All Videos', count: videos?.length || 0 },
                { key: 'published', label: 'Published', count: videos?.filter(v => v.published).length || 0 },
                { key: 'draft', label: 'Drafts', count: videos?.filter(v => !v.published).length || 0 },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setSelectedFilter(tab.key as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    selectedFilter === tab.key
                      ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  {tab.label}
                  <span className="ml-2 bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-300 py-0.5 px-2.5 rounded-full text-xs">
                    {tab.count}
                  </span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Videos Grid */}
        {videosLoading ? (
          <div className="flex items-center justify-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        ) : filteredVideos.length > 0 ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          >
            {filteredVideos.map((video, index) => (
              <motion.div
                key={video.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <VideoCard
                  video={video}
                  onPlay={() => {/* Handle video play */}}
                  onEdit={() => {/* Handle video edit */}}
                  onDelete={() => {/* Handle video delete */}}
                  onPublish={() => {/* Handle video publish */}}
                />
              </motion.div>
            ))}
          </motion.div>
        ) : (
          <EmptyState
            icon={VideoIcon}
            title="No videos yet"
            description="Create your first viral video to get started"
            action={{
              label: 'Create Video',
              onClick: () => setShowCreateModal(true),
              disabled: needsUpgrade,
            }}
          />
        )}

        {/* Recent Activity */}
        <div className="mt-12">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
            Recent Activity
          </h2>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="p-6">
              <div className="space-y-4">
                {/* Activity items would go here */}
                <div className="flex items-center space-x-3 text-sm text-gray-600 dark:text-gray-400">
                  <ClockIcon className="w-4 h-4" />
                  <span>Video "Trending Topic #1" generated successfully</span>
                  <span className="text-xs">2 hours ago</span>
                </div>
                
                <div className="flex items-center space-x-3 text-sm text-gray-600 dark:text-gray-400">
                  <TrendingUpIcon className="w-4 h-4" />
                  <span>Your video reached 10K views on TikTok</span>
                  <span className="text-xs">1 day ago</span>
                </div>
                
                <div className="flex items-center space-x-3 text-sm text-gray-600 dark:text-gray-400">
                  <PlayIcon className="w-4 h-4" />
                  <span>Video published to Instagram Reels</span>
                  <span className="text-xs">2 days ago</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Create Video Modal */}
      <CreateVideoModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={() => {
          setShowCreateModal(false);
          refetch();
          fetchDashboardStats();
        }}
      />
    </div>
  );
}
