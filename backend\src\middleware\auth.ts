import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';

const prisma = new PrismaClient();

// Extend Express Request type to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        subscriptionTier: string;
        [key: string]: any;
      };
    }
  }
}

interface JwtPayload {
  userId: string;
  email: string;
  iat: number;
  exp: number;
}

// Main authentication middleware
export const authMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = extractToken(req);
    
    if (!token) {
      res.status(401).json({
        error: 'Unauthorized',
        message: 'Access token is required',
      });
      return;
    }

    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as JwtPayload;
    
    // Fetch user from database
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        subscriptionTier: true,
        subscriptionEnds: true,
        emailVerified: true,
        createdAt: true,
        lastLoginAt: true,
      },
    });

    if (!user) {
      res.status(401).json({
        error: 'Unauthorized',
        message: 'User not found',
      });
      return;
    }

    // Check if email is verified for certain operations
    if (!user.emailVerified && requiresEmailVerification(req.path)) {
      res.status(403).json({
        error: 'Email Verification Required',
        message: 'Please verify your email address to access this feature',
      });
      return;
    }

    // Check subscription status
    if (user.subscriptionEnds && new Date() > user.subscriptionEnds) {
      // Downgrade to free tier if subscription expired
      await prisma.user.update({
        where: { id: user.id },
        data: { subscriptionTier: 'FREE' },
      });
      user.subscriptionTier = 'FREE';
    }

    // Update last login time
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() },
    });

    // Attach user to request
    req.user = user;
    next();

  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      res.status(401).json({
        error: 'Unauthorized',
        message: 'Invalid access token',
      });
      return;
    }

    if (error instanceof jwt.TokenExpiredError) {
      res.status(401).json({
        error: 'Token Expired',
        message: 'Access token has expired',
      });
      return;
    }

    logger.error('Authentication middleware error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Authentication failed',
    });
  }
};

// Optional authentication middleware (doesn't fail if no token)
export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = extractToken(req);
    
    if (!token) {
      next();
      return;
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as JwtPayload;
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        username: true,
        subscriptionTier: true,
        emailVerified: true,
      },
    });

    if (user) {
      req.user = user;
    }

    next();
  } catch (error) {
    // Silently continue without user if token is invalid
    next();
  }
};

// Subscription tier authorization
export const requireSubscription = (minTier: string) => {
  const tierHierarchy = ['FREE', 'STARTER', 'PRO', 'AGENCY', 'ENTERPRISE'];
  
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required',
      });
      return;
    }

    const userTierIndex = tierHierarchy.indexOf(req.user.subscriptionTier);
    const requiredTierIndex = tierHierarchy.indexOf(minTier);

    if (userTierIndex < requiredTierIndex) {
      res.status(403).json({
        error: 'Subscription Required',
        message: `This feature requires ${minTier} subscription or higher`,
        currentTier: req.user.subscriptionTier,
        requiredTier: minTier,
        upgradeUrl: '/pricing',
      });
      return;
    }

    next();
  };
};

// API key authentication for external API access
export const apiKeyAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const apiKey = req.headers['x-api-key'] as string;
    
    if (!apiKey) {
      res.status(401).json({
        error: 'API Key Required',
        message: 'X-API-Key header is required',
      });
      return;
    }

    // Fetch API key from database
    const keyRecord = await prisma.apiKey.findUnique({
      where: { key: apiKey },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            subscriptionTier: true,
            emailVerified: true,
          },
        },
      },
    });

    if (!keyRecord || !keyRecord.isActive) {
      res.status(401).json({
        error: 'Invalid API Key',
        message: 'The provided API key is invalid or inactive',
      });
      return;
    }

    // Update last used timestamp
    await prisma.apiKey.update({
      where: { id: keyRecord.id },
      data: { lastUsed: new Date() },
    });

    // Attach user to request
    req.user = keyRecord.user;
    next();

  } catch (error) {
    logger.error('API key authentication error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'API key authentication failed',
    });
  }
};

// Admin authorization
export const requireAdmin = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user) {
    res.status(401).json({
      error: 'Unauthorized',
      message: 'Authentication required',
    });
    return;
  }

  // Check if user has admin role (you might store this in user table)
  if (req.user.subscriptionTier !== 'ENTERPRISE' && !req.user.isAdmin) {
    res.status(403).json({
      error: 'Forbidden',
      message: 'Admin access required',
    });
    return;
  }

  next();
};

// Helper functions
function extractToken(req: Request): string | null {
  // Check Authorization header
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // Check cookie
  const cookieToken = req.cookies?.accessToken;
  if (cookieToken) {
    return cookieToken;
  }

  // Check query parameter (less secure, use with caution)
  const queryToken = req.query.token as string;
  if (queryToken) {
    return queryToken;
  }

  return null;
}

function requiresEmailVerification(path: string): boolean {
  const protectedPaths = [
    '/api/videos/generate',
    '/api/videos/publish',
    '/api/subscriptions',
  ];
  
  return protectedPaths.some(protectedPath => path.startsWith(protectedPath));
}

// Generate JWT token
export const generateTokens = (userId: string, email: string) => {
  const accessToken = jwt.sign(
    { userId, email },
    process.env.JWT_SECRET!,
    { expiresIn: '15m' }
  );

  const refreshToken = jwt.sign(
    { userId, email },
    process.env.JWT_REFRESH_SECRET!,
    { expiresIn: '7d' }
  );

  return { accessToken, refreshToken };
};

// Verify refresh token
export const verifyRefreshToken = (token: string): JwtPayload => {
  return jwt.verify(token, process.env.JWT_REFRESH_SECRET!) as JwtPayload;
};
