'use client';

import { useState, useEffect, useCallback } from 'react';
import { apiRequest } from './useAuth';
import toast from 'react-hot-toast';

export interface Video {
  id: string;
  title: string;
  description?: string;
  script: string;
  duration: number;
  status: 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'PUBLISHED';
  published: boolean;
  publishedAt?: string;
  thumbnailUrl?: string;
  fileUrl?: string;
  platforms: string[];
  voiceId: string;
  voiceSettings: {
    speed: number;
    pitch: number;
    emotion: string;
  };
  imagePrompts: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  analytics?: {
    views: number;
    likes: number;
    shares: number;
    comments: number;
  };
}

export interface VideoGenerationConfig {
  templateId: string;
  niche: string;
  topic?: string;
  duration: number;
  voiceId: string;
  voiceSettings: {
    speed: number;
    pitch: number;
    emotion: 'neutral' | 'excited' | 'serious' | 'friendly';
  };
  platforms: string[];
  customization: {
    colors?: string[];
    fonts?: string[];
    effects?: string[];
  };
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

export function useVideos() {
  const [videos, setVideos] = useState<Video[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0,
  });

  // Fetch videos with filters
  const fetchVideos = useCallback(async (params: {
    page?: number;
    limit?: number;
    status?: string;
    published?: boolean;
  } = {}) => {
    try {
      setIsLoading(true);
      setError(null);

      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append('page', params.page.toString());
      if (params.limit) queryParams.append('limit', params.limit.toString());
      if (params.status) queryParams.append('status', params.status);
      if (params.published !== undefined) queryParams.append('published', params.published.toString());

      const response = await apiRequest(`/api/videos?${queryParams.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch videos');
      }

      const data = await response.json();
      setVideos(data.videos);
      setPagination(data.pagination);

    } catch (err: any) {
      setError(err.message);
      toast.error(err.message || 'Failed to fetch videos');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Initial fetch
  useEffect(() => {
    fetchVideos();
  }, [fetchVideos]);

  // Generate new video
  const generateVideo = async (config: VideoGenerationConfig): Promise<{ videoId: string; status: string }> => {
    try {
      const response = await apiRequest('/api/videos/generate', {
        method: 'POST',
        body: JSON.stringify(config),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to generate video');
      }

      toast.success('Video generation started! This may take a few minutes.');
      
      // Refresh videos list
      await fetchVideos();

      return data;

    } catch (error: any) {
      toast.error(error.message || 'Failed to generate video');
      throw error;
    }
  };

  // Get specific video
  const getVideo = async (videoId: string): Promise<Video> => {
    try {
      const response = await apiRequest(`/api/videos/${videoId}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch video');
      }

      return await response.json();

    } catch (error: any) {
      toast.error(error.message || 'Failed to fetch video');
      throw error;
    }
  };

  // Update video
  const updateVideo = async (videoId: string, updates: {
    title?: string;
    description?: string;
    published?: boolean;
  }): Promise<Video> => {
    try {
      const response = await apiRequest(`/api/videos/${videoId}`, {
        method: 'PUT',
        body: JSON.stringify(updates),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to update video');
      }

      // Update local state
      setVideos(prev => prev.map(video => 
        video.id === videoId ? { ...video, ...data } : video
      ));

      toast.success('Video updated successfully!');
      return data;

    } catch (error: any) {
      toast.error(error.message || 'Failed to update video');
      throw error;
    }
  };

  // Delete video
  const deleteVideo = async (videoId: string): Promise<void> => {
    try {
      const response = await apiRequest(`/api/videos/${videoId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete video');
      }

      // Remove from local state
      setVideos(prev => prev.filter(video => video.id !== videoId));
      
      toast.success('Video deleted successfully!');

    } catch (error: any) {
      toast.error(error.message || 'Failed to delete video');
      throw error;
    }
  };

  // Publish video to platforms
  const publishVideo = async (videoId: string, platforms: string[], scheduledFor?: Date): Promise<void> => {
    try {
      const response = await apiRequest(`/api/videos/${videoId}/publish`, {
        method: 'POST',
        body: JSON.stringify({
          platforms,
          scheduledFor: scheduledFor?.toISOString(),
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to publish video');
      }

      // Update local state
      setVideos(prev => prev.map(video => 
        video.id === videoId 
          ? { ...video, published: true, publishedAt: new Date().toISOString() }
          : video
      ));

      toast.success(`Video publishing initiated for ${platforms.join(', ')}`);

    } catch (error: any) {
      toast.error(error.message || 'Failed to publish video');
      throw error;
    }
  };

  // Poll for video status updates
  const pollVideoStatus = useCallback(async (videoId: string): Promise<void> => {
    try {
      const video = await getVideo(videoId);
      
      // Update local state
      setVideos(prev => prev.map(v => v.id === videoId ? video : v));

      // If still processing, continue polling
      if (video.status === 'PROCESSING') {
        setTimeout(() => pollVideoStatus(videoId), 5000); // Poll every 5 seconds
      } else if (video.status === 'COMPLETED') {
        toast.success(`Video "${video.title}" is ready!`);
      } else if (video.status === 'FAILED') {
        toast.error(`Video "${video.title}" generation failed`);
      }

    } catch (error) {
      console.error('Error polling video status:', error);
    }
  }, []);

  // Start polling for processing videos
  useEffect(() => {
    const processingVideos = videos.filter(video => video.status === 'PROCESSING');
    
    processingVideos.forEach(video => {
      pollVideoStatus(video.id);
    });
  }, [videos, pollVideoStatus]);

  // Refetch videos
  const refetch = useCallback(() => {
    return fetchVideos();
  }, [fetchVideos]);

  // Load more videos (pagination)
  const loadMore = useCallback(async () => {
    if (pagination.page < pagination.pages) {
      await fetchVideos({ page: pagination.page + 1 });
    }
  }, [fetchVideos, pagination]);

  // Filter videos
  const filterVideos = useCallback(async (filters: {
    status?: string;
    published?: boolean;
  }) => {
    await fetchVideos({ ...filters, page: 1 });
  }, [fetchVideos]);

  return {
    videos,
    isLoading,
    error,
    pagination,
    generateVideo,
    getVideo,
    updateVideo,
    deleteVideo,
    publishVideo,
    refetch,
    loadMore,
    filterVideos,
    pollVideoStatus,
  };
}

// Hook for video analytics
export function useVideoAnalytics(videoId: string) {
  const [analytics, setAnalytics] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchAnalytics = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await apiRequest(`/api/analytics/videos/${videoId}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch analytics');
      }

      const data = await response.json();
      setAnalytics(data);

    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, [videoId]);

  useEffect(() => {
    if (videoId) {
      fetchAnalytics();
    }
  }, [videoId, fetchAnalytics]);

  return {
    analytics,
    isLoading,
    error,
    refetch: fetchAnalytics,
  };
}

// Hook for templates
export function useTemplates() {
  const [templates, setTemplates] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTemplates = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await apiRequest('/api/templates');
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch templates');
      }

      const data = await response.json();
      setTemplates(data);

    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchTemplates();
  }, [fetchTemplates]);

  return {
    templates,
    isLoading,
    error,
    refetch: fetchTemplates,
  };
}
