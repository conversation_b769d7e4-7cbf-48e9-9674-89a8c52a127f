'use client';

import { useState, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { XMarkIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';
import { useVideos, VideoGenerationConfig } from '../hooks/useVideos';
import { useTemplates } from '../hooks/useVideos';
import { useAuth, useSubscription } from '../hooks/useAuth';
import toast from 'react-hot-toast';

interface CreateVideoModalProps {
  isOpen: boolean;
  onClose: () => void;
  onVideoCreated?: (videoId: string) => void;
}

const NICHES = [
  'Technology',
  'Business',
  'Health & Fitness',
  'Entertainment',
  'Education',
  'Travel',
  'Food & Cooking',
  'Fashion',
  'Gaming',
  'Sports',
  'Music',
  'Art & Design',
  'Science',
  'News',
  'Lifestyle',
];

const PLATFORMS = [
  { id: 'tiktok', name: 'TikTok', icon: '🎵' },
  { id: 'youtube', name: 'YouTube Shorts', icon: '📺' },
  { id: 'instagram', name: 'Instagram Reels', icon: '📸' },
  { id: 'twitter', name: 'Twitter', icon: '🐦' },
];

const VOICE_EMOTIONS = [
  { id: 'neutral', name: 'Neutral', description: 'Balanced and professional' },
  { id: 'excited', name: 'Excited', description: 'Energetic and enthusiastic' },
  { id: 'serious', name: 'Serious', description: 'Formal and authoritative' },
  { id: 'friendly', name: 'Friendly', description: 'Warm and approachable' },
];

export default function CreateVideoModal({
  isOpen,
  onClose,
  onVideoCreated,
}: CreateVideoModalProps) {
  const { generateVideo } = useVideos();
  const { templates, isLoading: templatesLoading } = useTemplates();
  const { user } = useAuth();
  const { hasFeature, canGenerateVideo } = useSubscription();

  const [isGenerating, setIsGenerating] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [config, setConfig] = useState<Partial<VideoGenerationConfig>>({
    templateId: '',
    niche: '',
    topic: '',
    duration: 30,
    voiceId: 'default',
    voiceSettings: {
      speed: 1.0,
      pitch: 1.0,
      emotion: 'neutral',
    },
    platforms: ['tiktok'],
    customization: {
      colors: ['#6366f1', '#8b5cf6'],
      fonts: ['Inter'],
      effects: ['fade'],
    },
  });

  const totalSteps = 4;

  useEffect(() => {
    if (templates.length > 0 && !config.templateId) {
      setConfig(prev => ({ ...prev, templateId: templates[0].id }));
    }
  }, [templates, config.templateId]);

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleGenerate = async () => {
    if (!canGenerateVideo()) {
      toast.error('You have reached your monthly video limit. Please upgrade your plan.');
      return;
    }

    if (!config.templateId || !config.niche || !config.platforms?.length) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      setIsGenerating(true);
      
      const result = await generateVideo(config as VideoGenerationConfig);
      
      toast.success('Video generation started! Check your dashboard for progress.');
      
      if (onVideoCreated) {
        onVideoCreated(result.videoId);
      }
      
      onClose();
      
    } catch (error: any) {
      toast.error(error.message || 'Failed to generate video');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleClose = () => {
    if (!isGenerating) {
      setCurrentStep(1);
      setConfig({
        templateId: templates[0]?.id || '',
        niche: '',
        topic: '',
        duration: 30,
        voiceId: 'default',
        voiceSettings: {
          speed: 1.0,
          pitch: 1.0,
          emotion: 'neutral',
        },
        platforms: ['tiktok'],
        customization: {
          colors: ['#6366f1', '#8b5cf6'],
          fonts: ['Inter'],
          effects: ['fade'],
        },
      });
      onClose();
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Choose Template & Niche
              </h3>
              
              {/* Template Selection */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Template
                </label>
                {templatesLoading ? (
                  <div className="grid grid-cols-2 gap-3">
                    {[1, 2, 3, 4].map(i => (
                      <div key={i} className="animate-pulse">
                        <div className="h-20 bg-gray-200 rounded-lg"></div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="grid grid-cols-2 gap-3">
                    {templates.map(template => (
                      <button
                        key={template.id}
                        onClick={() => setConfig(prev => ({ ...prev, templateId: template.id }))}
                        className={`p-4 rounded-lg border-2 text-left transition-all ${
                          config.templateId === template.id
                            ? 'border-primary-500 bg-primary-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <div className="font-medium text-gray-900">{template.name}</div>
                        <div className="text-sm text-gray-500 mt-1">{template.description}</div>
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Niche Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Niche
                </label>
                <div className="grid grid-cols-3 gap-2">
                  {NICHES.map(niche => (
                    <button
                      key={niche}
                      onClick={() => setConfig(prev => ({ ...prev, niche }))}
                      className={`px-3 py-2 rounded-lg text-sm transition-all ${
                        config.niche === niche
                          ? 'bg-primary-500 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {niche}
                    </button>
                  ))}
                </div>
              </div>

              {/* Topic Input */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Topic (Optional)
                </label>
                <input
                  type="text"
                  value={config.topic || ''}
                  onChange={(e) => setConfig(prev => ({ ...prev, topic: e.target.value }))}
                  placeholder="Enter a specific topic or leave blank for trending content"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Video Settings
            </h3>

            {/* Duration */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Duration: {config.duration} seconds
              </label>
              <input
                type="range"
                min="15"
                max="180"
                step="15"
                value={config.duration}
                onChange={(e) => setConfig(prev => ({ ...prev, duration: parseInt(e.target.value) }))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>15s</span>
                <span>60s</span>
                <span>180s</span>
              </div>
            </div>

            {/* Platforms */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Target Platforms
              </label>
              <div className="grid grid-cols-2 gap-3">
                {PLATFORMS.map(platform => (
                  <button
                    key={platform.id}
                    onClick={() => {
                      const platforms = config.platforms || [];
                      const newPlatforms = platforms.includes(platform.id)
                        ? platforms.filter(p => p !== platform.id)
                        : [...platforms, platform.id];
                      setConfig(prev => ({ ...prev, platforms: newPlatforms }));
                    }}
                    className={`p-3 rounded-lg border-2 text-left transition-all ${
                      config.platforms?.includes(platform.id)
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center">
                      <span className="text-lg mr-2">{platform.icon}</span>
                      <span className="font-medium">{platform.name}</span>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Voice Settings
            </h3>

            {/* Voice Emotion */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Voice Style
              </label>
              <div className="space-y-2">
                {VOICE_EMOTIONS.map(emotion => (
                  <button
                    key={emotion.id}
                    onClick={() => setConfig(prev => ({
                      ...prev,
                      voiceSettings: { ...prev.voiceSettings!, emotion: emotion.id as any }
                    }))}
                    className={`w-full p-3 rounded-lg border-2 text-left transition-all ${
                      config.voiceSettings?.emotion === emotion.id
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="font-medium text-gray-900">{emotion.name}</div>
                    <div className="text-sm text-gray-500">{emotion.description}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* Voice Speed */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Speed: {config.voiceSettings?.speed}x
              </label>
              <input
                type="range"
                min="0.5"
                max="2.0"
                step="0.1"
                value={config.voiceSettings?.speed}
                onChange={(e) => setConfig(prev => ({
                  ...prev,
                  voiceSettings: { ...prev.voiceSettings!, speed: parseFloat(e.target.value) }
                }))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
              />
            </div>

            {/* Voice Pitch */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Pitch: {config.voiceSettings?.pitch}x
              </label>
              <input
                type="range"
                min="0.5"
                max="2.0"
                step="0.1"
                value={config.voiceSettings?.pitch}
                onChange={(e) => setConfig(prev => ({
                  ...prev,
                  voiceSettings: { ...prev.voiceSettings!, pitch: parseFloat(e.target.value) }
                }))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
              />
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Review & Generate
            </h3>

            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Template:</span>
                <span className="text-sm font-medium">
                  {templates.find(t => t.id === config.templateId)?.name}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Niche:</span>
                <span className="text-sm font-medium">{config.niche}</span>
              </div>
              {config.topic && (
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Topic:</span>
                  <span className="text-sm font-medium">{config.topic}</span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Duration:</span>
                <span className="text-sm font-medium">{config.duration}s</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Platforms:</span>
                <span className="text-sm font-medium">
                  {config.platforms?.map(p => 
                    PLATFORMS.find(platform => platform.id === p)?.name
                  ).join(', ')}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Voice:</span>
                <span className="text-sm font-medium">
                  {VOICE_EMOTIONS.find(e => e.id === config.voiceSettings?.emotion)?.name}
                </span>
              </div>
            </div>

            {!canGenerateVideo() && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <SparklesIcon className="h-5 w-5 text-yellow-400" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-yellow-800">
                      Monthly Limit Reached
                    </h3>
                    <div className="mt-2 text-sm text-yellow-700">
                      <p>
                        You've reached your monthly video generation limit. 
                        Upgrade your plan to create more videos.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={handleClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-6">
                  <Dialog.Title as="h3" className="text-xl font-semibold text-gray-900">
                    Create New Video
                  </Dialog.Title>
                  <button
                    onClick={handleClose}
                    disabled={isGenerating}
                    className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                {/* Progress Bar */}
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">
                      Step {currentStep} of {totalSteps}
                    </span>
                    <span className="text-sm text-gray-500">
                      {Math.round((currentStep / totalSteps) * 100)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <motion.div
                      className="bg-primary-500 h-2 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ width: `${(currentStep / totalSteps) * 100}%` }}
                      transition={{ duration: 0.3 }}
                    />
                  </div>
                </div>

                {/* Step Content */}
                <div className="mb-8">
                  {renderStep()}
                </div>

                {/* Navigation Buttons */}
                <div className="flex justify-between">
                  <button
                    onClick={handlePrevious}
                    disabled={currentStep === 1 || isGenerating}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>

                  <div className="flex space-x-3">
                    {currentStep < totalSteps ? (
                      <button
                        onClick={handleNext}
                        disabled={isGenerating}
                        className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Next
                      </button>
                    ) : (
                      <button
                        onClick={handleGenerate}
                        disabled={isGenerating || !canGenerateVideo()}
                        className="px-6 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                      >
                        {isGenerating ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Generating...
                          </>
                        ) : (
                          <>
                            <SparklesIcon className="h-4 w-4 mr-2" />
                            Generate Video
                          </>
                        )}
                      </button>
                    )}
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
