import OpenAI from 'openai';
import axios from 'axios';
import ffmpeg from 'fluent-ffmpeg';
import path from 'path';
import fs from 'fs/promises';
import { v4 as uuidv4 } from 'uuid';
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import { uploadToS3 } from '../utils/storage';
import { addToQueue } from './queue';

const prisma = new PrismaClient();

// Initialize OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Video generation configuration
interface VideoConfig {
  templateId: string;
  niche: string;
  topic?: string;
  duration: number;
  voiceId: string;
  voiceSettings: {
    speed: number;
    pitch: number;
    emotion: 'neutral' | 'excited' | 'serious' | 'friendly';
  };
  platforms: string[];
  customization: {
    colors?: string[];
    fonts?: string[];
    effects?: string[];
  };
}

interface GenerationResult {
  videoId: string;
  status: 'processing' | 'completed' | 'failed';
  fileUrl?: string;
  thumbnailUrl?: string;
  error?: string;
}

export class VideoGenerationService {
  private tempDir: string;

  constructor() {
    this.tempDir = path.join(process.cwd(), 'temp');
    this.ensureTempDir();
  }

  private async ensureTempDir(): Promise<void> {
    try {
      await fs.access(this.tempDir);
    } catch {
      await fs.mkdir(this.tempDir, { recursive: true });
    }
  }

  // Main video generation function
  async generateVideo(
    userId: string,
    projectId: string,
    config: VideoConfig
  ): Promise<GenerationResult> {
    const videoId = uuidv4();
    
    try {
      logger.info(`Starting video generation for user ${userId}`, { videoId, config });

      // Create video record in database
      const video = await prisma.video.create({
        data: {
          id: videoId,
          title: `Generated Video - ${new Date().toISOString()}`,
          script: '',
          duration: config.duration,
          status: 'PROCESSING',
          platforms: config.platforms,
          voiceId: config.voiceId,
          voiceSettings: config.voiceSettings,
          imagePrompts: {},
          userId,
          projectId,
        },
      });

      // Add to processing queue
      await addToQueue('video-generation', {
        videoId,
        userId,
        config,
      });

      return {
        videoId,
        status: 'processing',
      };

    } catch (error) {
      logger.error('Video generation initiation failed:', error);
      return {
        videoId,
        status: 'failed',
        error: 'Failed to initiate video generation',
      };
    }
  }

  // Process video generation (called by queue worker)
  async processVideoGeneration(data: {
    videoId: string;
    userId: string;
    config: VideoConfig;
  }): Promise<void> {
    const { videoId, userId, config } = data;
    
    try {
      // Step 1: Generate script using AI
      const script = await this.generateScript(config);
      
      // Step 2: Generate voice narration
      const audioPath = await this.generateVoice(script, config.voiceSettings);
      
      // Step 3: Generate images based on script
      const imagePaths = await this.generateImages(script, config);
      
      // Step 4: Compose video
      const videoPath = await this.composeVideo(audioPath, imagePaths, config);
      
      // Step 5: Generate thumbnail
      const thumbnailPath = await this.generateThumbnail(videoPath);
      
      // Step 6: Upload to storage
      const fileUrl = await uploadToS3(videoPath, `videos/${videoId}.mp4`);
      const thumbnailUrl = await uploadToS3(thumbnailPath, `thumbnails/${videoId}.jpg`);
      
      // Step 7: Update database
      await prisma.video.update({
        where: { id: videoId },
        data: {
          script,
          status: 'COMPLETED',
          fileUrl,
          thumbnailUrl,
        },
      });

      // Step 8: Clean up temporary files
      await this.cleanup([audioPath, videoPath, thumbnailPath, ...imagePaths]);
      
      logger.info(`Video generation completed successfully`, { videoId });

    } catch (error) {
      logger.error('Video generation processing failed:', error);
      
      // Update video status to failed
      await prisma.video.update({
        where: { id: videoId },
        data: {
          status: 'FAILED',
        },
      });
    }
  }

  // Generate script using OpenAI
  private async generateScript(config: VideoConfig): Promise<string> {
    const template = await prisma.template.findUnique({
      where: { id: config.templateId },
    });

    if (!template) {
      throw new Error('Template not found');
    }

    const prompt = `
      Create a viral ${config.duration}-second video script for ${config.niche} niche.
      Template style: ${template.name}
      Topic: ${config.topic || 'trending topic'}
      
      Requirements:
      - Hook viewers in first 3 seconds
      - Include trending elements
      - Optimize for ${config.platforms.join(', ')}
      - Keep it engaging and shareable
      - Use simple, clear language
      
      Return only the script text, no additional formatting.
    `;

    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'You are a viral content creator specializing in short-form videos.',
        },
        {
          role: 'user',
          content: prompt,
        },
      ],
      max_tokens: 500,
      temperature: 0.8,
    });

    return response.choices[0]?.message?.content || 'Default script content';
  }

  // Generate voice using ElevenLabs (or free alternative)
  private async generateVoice(
    script: string,
    voiceSettings: VideoConfig['voiceSettings']
  ): Promise<string> {
    const outputPath = path.join(this.tempDir, `audio_${uuidv4()}.mp3`);

    try {
      // Try ElevenLabs first if API key is available
      if (process.env.ELEVENLABS_API_KEY) {
        const response = await axios.post(
          'https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM',
          {
            text: script,
            voice_settings: {
              stability: 0.5,
              similarity_boost: 0.5,
              speed: voiceSettings.speed,
            },
          },
          {
            headers: {
              'xi-api-key': process.env.ELEVENLABS_API_KEY,
              'Content-Type': 'application/json',
            },
            responseType: 'arraybuffer',
          }
        );

        await fs.writeFile(outputPath, response.data);
        return outputPath;
      }

      // Fallback to free TTS (using system TTS or other free service)
      // This is a placeholder - implement with a free TTS service
      await this.generateFreeTTS(script, outputPath);
      return outputPath;

    } catch (error) {
      logger.error('Voice generation failed:', error);
      throw new Error('Failed to generate voice narration');
    }
  }

  // Free TTS implementation (placeholder)
  private async generateFreeTTS(script: string, outputPath: string): Promise<void> {
    // Implement free TTS service here
    // Options: Google TTS, Azure Cognitive Services free tier, or local TTS
    
    // For now, create a silent audio file as placeholder
    return new Promise((resolve, reject) => {
      ffmpeg()
        .input('anullsrc=channel_layout=stereo:sample_rate=48000')
        .inputFormat('lavfi')
        .duration(10) // 10 seconds of silence
        .output(outputPath)
        .on('end', () => resolve())
        .on('error', reject)
        .run();
    });
  }

  // Generate images using AI
  private async generateImages(script: string, config: VideoConfig): Promise<string[]> {
    const imagePaths: string[] = [];
    const scenes = this.extractScenes(script);

    for (let i = 0; i < scenes.length; i++) {
      try {
        const imagePath = await this.generateSingleImage(scenes[i], i);
        imagePaths.push(imagePath);
      } catch (error) {
        logger.error(`Failed to generate image for scene ${i}:`, error);
        // Use placeholder image
        const placeholderPath = await this.createPlaceholderImage(i);
        imagePaths.push(placeholderPath);
      }
    }

    return imagePaths;
  }

  // Extract scenes from script for image generation
  private extractScenes(script: string): string[] {
    // Simple scene extraction - split by sentences
    const sentences = script.split(/[.!?]+/).filter(s => s.trim().length > 0);
    return sentences.slice(0, 5); // Limit to 5 scenes
  }

  // Generate single image using AI
  private async generateSingleImage(scene: string, index: number): Promise<string> {
    const outputPath = path.join(this.tempDir, `image_${uuidv4()}.jpg`);

    if (process.env.OPENAI_API_KEY) {
      try {
        const response = await openai.images.generate({
          model: 'dall-e-3',
          prompt: `Create a vibrant, engaging image for: ${scene}. Style: modern, colorful, social media optimized`,
          size: '1024x1024',
          quality: 'standard',
          n: 1,
        });

        const imageUrl = response.data[0]?.url;
        if (imageUrl) {
          const imageResponse = await axios.get(imageUrl, { responseType: 'arraybuffer' });
          await fs.writeFile(outputPath, imageResponse.data);
          return outputPath;
        }
      } catch (error) {
        logger.error('DALL-E image generation failed:', error);
      }
    }

    // Fallback to placeholder
    return this.createPlaceholderImage(index);
  }

  // Create placeholder image
  private async createPlaceholderImage(index: number): Promise<string> {
    const outputPath = path.join(this.tempDir, `placeholder_${uuidv4()}.jpg`);
    
    return new Promise((resolve, reject) => {
      ffmpeg()
        .input(`color=c=blue:size=1080x1920:duration=1`)
        .inputFormat('lavfi')
        .output(outputPath)
        .on('end', () => resolve(outputPath))
        .on('error', reject)
        .run();
    });
  }

  // Compose final video
  private async composeVideo(
    audioPath: string,
    imagePaths: string[],
    config: VideoConfig
  ): Promise<string> {
    const outputPath = path.join(this.tempDir, `video_${uuidv4()}.mp4`);
    
    return new Promise((resolve, reject) => {
      let command = ffmpeg();

      // Add images as inputs
      imagePaths.forEach(imagePath => {
        command = command.input(imagePath);
      });

      // Add audio
      command = command.input(audioPath);

      // Configure video composition
      command
        .complexFilter([
          // Create slideshow from images
          `concat=n=${imagePaths.length}:v=1:a=0[video]`,
          // Scale to target resolution
          '[video]scale=1080:1920[scaled]',
        ])
        .map('[scaled]')
        .map(`${imagePaths.length}:a`) // Map audio
        .videoCodec('libx264')
        .audioCodec('aac')
        .format('mp4')
        .duration(config.duration)
        .output(outputPath)
        .on('end', () => resolve(outputPath))
        .on('error', reject)
        .run();
    });
  }

  // Generate thumbnail
  private async generateThumbnail(videoPath: string): Promise<string> {
    const outputPath = path.join(this.tempDir, `thumb_${uuidv4()}.jpg`);
    
    return new Promise((resolve, reject) => {
      ffmpeg(videoPath)
        .screenshots({
          timestamps: ['50%'],
          filename: path.basename(outputPath),
          folder: path.dirname(outputPath),
          size: '1080x1920',
        })
        .on('end', () => resolve(outputPath))
        .on('error', reject);
    });
  }

  // Clean up temporary files
  private async cleanup(filePaths: string[]): Promise<void> {
    for (const filePath of filePaths) {
      try {
        await fs.unlink(filePath);
      } catch (error) {
        logger.warn(`Failed to delete temporary file: ${filePath}`, error);
      }
    }
  }
}
