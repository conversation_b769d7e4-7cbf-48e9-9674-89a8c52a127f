import AWS from 'aws-sdk';
import { Storage } from '@google-cloud/storage';
import fs from 'fs/promises';
import path from 'path';
import { logger } from './logger';

// AWS S3 Configuration
const s3 = process.env.AWS_ACCESS_KEY_ID ? new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION || 'us-east-1',
}) : null;

// Google Cloud Storage Configuration
const gcs = process.env.GOOGLE_CLOUD_PROJECT_ID ? new Storage({
  projectId: process.env.GOOGLE_CLOUD_PROJECT_ID,
  keyFilename: process.env.GOOGLE_CLOUD_KEY_FILE,
}) : null;

// Storage provider type
export type StorageProvider = 'aws' | 'gcp' | 'local';

// Storage configuration
const STORAGE_CONFIG = {
  provider: (process.env.STORAGE_PROVIDER as StorageProvider) || 'local',
  aws: {
    bucket: process.env.AWS_S3_BUCKET || 'viralab-videos',
    region: process.env.AWS_REGION || 'us-east-1',
  },
  gcp: {
    bucket: process.env.GCP_STORAGE_BUCKET || 'viralab-videos',
  },
  local: {
    uploadDir: process.env.LOCAL_UPLOAD_DIR || './uploads',
  },
};

export interface UploadResult {
  url: string;
  key: string;
  size: number;
  contentType: string;
}

export class StorageService {
  private provider: StorageProvider;

  constructor(provider?: StorageProvider) {
    this.provider = provider || STORAGE_CONFIG.provider;
  }

  // Upload file to configured storage provider
  async uploadFile(
    filePath: string,
    key: string,
    options: {
      contentType?: string;
      metadata?: Record<string, string>;
      public?: boolean;
    } = {}
  ): Promise<UploadResult> {
    try {
      const fileStats = await fs.stat(filePath);
      const contentType = options.contentType || this.getContentType(filePath);

      switch (this.provider) {
        case 'aws':
          return await this.uploadToS3(filePath, key, { ...options, contentType });
        
        case 'gcp':
          return await this.uploadToGCS(filePath, key, { ...options, contentType });
        
        case 'local':
          return await this.uploadToLocal(filePath, key, { ...options, contentType });
        
        default:
          throw new Error(`Unsupported storage provider: ${this.provider}`);
      }

    } catch (error) {
      logger.error('File upload failed:', error);
      throw new Error(`Failed to upload file: ${error}`);
    }
  }

  // Upload to AWS S3
  private async uploadToS3(
    filePath: string,
    key: string,
    options: any
  ): Promise<UploadResult> {
    if (!s3) {
      throw new Error('AWS S3 not configured');
    }

    const fileBuffer = await fs.readFile(filePath);
    const fileStats = await fs.stat(filePath);

    const uploadParams = {
      Bucket: STORAGE_CONFIG.aws.bucket,
      Key: key,
      Body: fileBuffer,
      ContentType: options.contentType,
      Metadata: options.metadata || {},
      ACL: options.public ? 'public-read' : 'private',
    };

    const result = await s3.upload(uploadParams).promise();

    return {
      url: result.Location,
      key: result.Key,
      size: fileStats.size,
      contentType: options.contentType,
    };
  }

  // Upload to Google Cloud Storage
  private async uploadToGCS(
    filePath: string,
    key: string,
    options: any
  ): Promise<UploadResult> {
    if (!gcs) {
      throw new Error('Google Cloud Storage not configured');
    }

    const bucket = gcs.bucket(STORAGE_CONFIG.gcp.bucket);
    const file = bucket.file(key);
    const fileStats = await fs.stat(filePath);

    await bucket.upload(filePath, {
      destination: key,
      metadata: {
        contentType: options.contentType,
        metadata: options.metadata || {},
      },
      public: options.public || false,
    });

    const publicUrl = options.public 
      ? `https://storage.googleapis.com/${STORAGE_CONFIG.gcp.bucket}/${key}`
      : await file.getSignedUrl({
          action: 'read',
          expires: Date.now() + 1000 * 60 * 60 * 24 * 7, // 7 days
        }).then(urls => urls[0]);

    return {
      url: publicUrl,
      key,
      size: fileStats.size,
      contentType: options.contentType,
    };
  }

  // Upload to local storage
  private async uploadToLocal(
    filePath: string,
    key: string,
    options: any
  ): Promise<UploadResult> {
    const uploadDir = STORAGE_CONFIG.local.uploadDir;
    const destinationPath = path.join(uploadDir, key);
    const destinationDir = path.dirname(destinationPath);

    // Ensure directory exists
    await fs.mkdir(destinationDir, { recursive: true });

    // Copy file
    await fs.copyFile(filePath, destinationPath);

    const fileStats = await fs.stat(destinationPath);
    const baseUrl = process.env.BASE_URL || 'http://localhost:3001';

    return {
      url: `${baseUrl}/uploads/${key}`,
      key,
      size: fileStats.size,
      contentType: options.contentType,
    };
  }

  // Delete file from storage
  async deleteFile(key: string): Promise<void> {
    try {
      switch (this.provider) {
        case 'aws':
          await this.deleteFromS3(key);
          break;
        
        case 'gcp':
          await this.deleteFromGCS(key);
          break;
        
        case 'local':
          await this.deleteFromLocal(key);
          break;
        
        default:
          throw new Error(`Unsupported storage provider: ${this.provider}`);
      }

      logger.info(`File deleted successfully: ${key}`);

    } catch (error) {
      logger.error('File deletion failed:', error);
      throw new Error(`Failed to delete file: ${error}`);
    }
  }

  // Delete from AWS S3
  private async deleteFromS3(key: string): Promise<void> {
    if (!s3) {
      throw new Error('AWS S3 not configured');
    }

    await s3.deleteObject({
      Bucket: STORAGE_CONFIG.aws.bucket,
      Key: key,
    }).promise();
  }

  // Delete from Google Cloud Storage
  private async deleteFromGCS(key: string): Promise<void> {
    if (!gcs) {
      throw new Error('Google Cloud Storage not configured');
    }

    const bucket = gcs.bucket(STORAGE_CONFIG.gcp.bucket);
    await bucket.file(key).delete();
  }

  // Delete from local storage
  private async deleteFromLocal(key: string): Promise<void> {
    const filePath = path.join(STORAGE_CONFIG.local.uploadDir, key);
    await fs.unlink(filePath);
  }

  // Get signed URL for private files
  async getSignedUrl(
    key: string,
    expiresIn: number = 3600 // 1 hour
  ): Promise<string> {
    try {
      switch (this.provider) {
        case 'aws':
          return await this.getS3SignedUrl(key, expiresIn);
        
        case 'gcp':
          return await this.getGCSSignedUrl(key, expiresIn);
        
        case 'local':
          // For local storage, return direct URL
          const baseUrl = process.env.BASE_URL || 'http://localhost:3001';
          return `${baseUrl}/uploads/${key}`;
        
        default:
          throw new Error(`Unsupported storage provider: ${this.provider}`);
      }

    } catch (error) {
      logger.error('Failed to generate signed URL:', error);
      throw new Error(`Failed to generate signed URL: ${error}`);
    }
  }

  // Get S3 signed URL
  private async getS3SignedUrl(key: string, expiresIn: number): Promise<string> {
    if (!s3) {
      throw new Error('AWS S3 not configured');
    }

    return s3.getSignedUrl('getObject', {
      Bucket: STORAGE_CONFIG.aws.bucket,
      Key: key,
      Expires: expiresIn,
    });
  }

  // Get GCS signed URL
  private async getGCSSignedUrl(key: string, expiresIn: number): Promise<string> {
    if (!gcs) {
      throw new Error('Google Cloud Storage not configured');
    }

    const bucket = gcs.bucket(STORAGE_CONFIG.gcp.bucket);
    const file = bucket.file(key);

    const [url] = await file.getSignedUrl({
      action: 'read',
      expires: Date.now() + expiresIn * 1000,
    });

    return url;
  }

  // Get content type from file extension
  private getContentType(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();
    
    const contentTypes: Record<string, string> = {
      '.mp4': 'video/mp4',
      '.avi': 'video/avi',
      '.mov': 'video/quicktime',
      '.wmv': 'video/x-ms-wmv',
      '.flv': 'video/x-flv',
      '.webm': 'video/webm',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.mp3': 'audio/mpeg',
      '.wav': 'audio/wav',
      '.ogg': 'audio/ogg',
      '.m4a': 'audio/mp4',
    };

    return contentTypes[ext] || 'application/octet-stream';
  }

  // List files in storage
  async listFiles(prefix?: string, limit?: number): Promise<Array<{
    key: string;
    size: number;
    lastModified: Date;
    contentType: string;
  }>> {
    try {
      switch (this.provider) {
        case 'aws':
          return await this.listS3Files(prefix, limit);
        
        case 'gcp':
          return await this.listGCSFiles(prefix, limit);
        
        case 'local':
          return await this.listLocalFiles(prefix, limit);
        
        default:
          throw new Error(`Unsupported storage provider: ${this.provider}`);
      }

    } catch (error) {
      logger.error('Failed to list files:', error);
      throw new Error(`Failed to list files: ${error}`);
    }
  }

  // List S3 files
  private async listS3Files(prefix?: string, limit?: number): Promise<any[]> {
    if (!s3) {
      throw new Error('AWS S3 not configured');
    }

    const params: AWS.S3.ListObjectsV2Request = {
      Bucket: STORAGE_CONFIG.aws.bucket,
      Prefix: prefix,
      MaxKeys: limit,
    };

    const result = await s3.listObjectsV2(params).promise();
    
    return (result.Contents || []).map(obj => ({
      key: obj.Key!,
      size: obj.Size!,
      lastModified: obj.LastModified!,
      contentType: this.getContentType(obj.Key!),
    }));
  }

  // List GCS files
  private async listGCSFiles(prefix?: string, limit?: number): Promise<any[]> {
    if (!gcs) {
      throw new Error('Google Cloud Storage not configured');
    }

    const bucket = gcs.bucket(STORAGE_CONFIG.gcp.bucket);
    const [files] = await bucket.getFiles({
      prefix,
      maxResults: limit,
    });

    return files.map(file => ({
      key: file.name,
      size: parseInt(file.metadata.size || '0'),
      lastModified: new Date(file.metadata.timeCreated),
      contentType: file.metadata.contentType || this.getContentType(file.name),
    }));
  }

  // List local files
  private async listLocalFiles(prefix?: string, limit?: number): Promise<any[]> {
    const uploadDir = STORAGE_CONFIG.local.uploadDir;
    const searchDir = prefix ? path.join(uploadDir, prefix) : uploadDir;

    try {
      const files = await fs.readdir(searchDir, { withFileTypes: true });
      const fileList = [];

      for (const file of files) {
        if (file.isFile()) {
          const filePath = path.join(searchDir, file.name);
          const stats = await fs.stat(filePath);
          const relativePath = path.relative(uploadDir, filePath);

          fileList.push({
            key: relativePath.replace(/\\/g, '/'), // Normalize path separators
            size: stats.size,
            lastModified: stats.mtime,
            contentType: this.getContentType(filePath),
          });

          if (limit && fileList.length >= limit) {
            break;
          }
        }
      }

      return fileList;

    } catch (error) {
      return [];
    }
  }
}

// Default storage service instance
export const storageService = new StorageService();

// Convenience functions
export const uploadToS3 = (filePath: string, key: string, options?: any) => 
  new StorageService('aws').uploadFile(filePath, key, options);

export const uploadToGCS = (filePath: string, key: string, options?: any) => 
  new StorageService('gcp').uploadFile(filePath, key, options);

export const uploadToLocal = (filePath: string, key: string, options?: any) => 
  new StorageService('local').uploadFile(filePath, key, options);
