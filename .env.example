# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/viralab"
REDIS_URL="redis://localhost:6379"

# Application Settings
NODE_ENV="development"
PORT=3001
FRONTEND_URL="http://localhost:3000"
API_BASE_URL="http://localhost:3001"

# Security
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_REFRESH_SECRET="your-super-secret-refresh-key-change-this-in-production"
ENCRYPTION_KEY="your-32-character-encryption-key-here"
SESSION_SECRET="your-session-secret-key-change-this"

# AI Services
OPENAI_API_KEY="sk-your-openai-api-key"
ELEVENLABS_API_KEY="your-elevenlabs-api-key"
HUGGINGFACE_API_KEY="hf_your-huggingface-token"
STABILITY_AI_API_KEY="sk-your-stability-ai-key"

# AWS Configuration
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="viralab-videos"
AWS_CLOUDFRONT_DOMAIN="your-cloudfront-domain.cloudfront.net"

# Social Media APIs
YOUTUBE_CLIENT_ID="your-youtube-client-id"
YOUTUBE_CLIENT_SECRET="your-youtube-client-secret"
YOUTUBE_REDIRECT_URI="http://localhost:3001/auth/youtube/callback"

TIKTOK_CLIENT_KEY="your-tiktok-client-key"
TIKTOK_CLIENT_SECRET="your-tiktok-client-secret"
TIKTOK_REDIRECT_URI="http://localhost:3001/auth/tiktok/callback"

INSTAGRAM_APP_ID="your-instagram-app-id"
INSTAGRAM_APP_SECRET="your-instagram-app-secret"
INSTAGRAM_REDIRECT_URI="http://localhost:3001/auth/instagram/callback"

TWITTER_API_KEY="your-twitter-api-key"
TWITTER_API_SECRET="your-twitter-api-secret"
TWITTER_ACCESS_TOKEN="your-twitter-access-token"
TWITTER_ACCESS_TOKEN_SECRET="your-twitter-access-token-secret"

# Payment Processing
STRIPE_PUBLISHABLE_KEY="pk_test_your-stripe-publishable-key"
STRIPE_SECRET_KEY="sk_test_your-stripe-secret-key"
STRIPE_WEBHOOK_SECRET="whsec_your-stripe-webhook-secret"

# Email Service
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
FROM_EMAIL="<EMAIL>"

# Google OAuth
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=true

# Monitoring & Analytics
SENTRY_DSN="your-sentry-dsn"
GOOGLE_ANALYTICS_ID="GA-XXXXXXXXX"

# Feature Flags
ENABLE_VOICE_CLONING=true
ENABLE_BATCH_PROCESSING=true
ENABLE_AUTO_PUBLISHING=true
ENABLE_ANALYTICS=true

# Development
DEBUG=true
LOG_LEVEL="debug"

# Deployment Specific
NETLIFY_SITE_ID="your-netlify-site-id"
VERCEL_PROJECT_ID="your-vercel-project-id"
GCP_PROJECT_ID="your-gcp-project-id"
