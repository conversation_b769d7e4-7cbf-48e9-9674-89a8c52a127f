// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String   @id @default(cuid())
  email             String   @unique
  username          String?  @unique
  firstName         String?
  lastName          String?
  avatar            String?
  emailVerified     Boolean  @default(false)
  emailVerifiedAt   DateTime?
  password          String?
  
  // Subscription
  subscriptionTier  SubscriptionTier @default(FREE)
  subscriptionId    String?
  customerId        String?
  subscriptionEnds  DateTime?
  
  // Usage tracking
  videosGenerated   Int      @default(0)
  monthlyUsage      Int      @default(0)
  lastUsageReset    DateTime @default(now())
  
  // Settings
  preferences       Json?
  timezone          String   @default("UTC")
  language          String   @default("en")
  
  // Social connections
  googleId          String?
  tiktokId          String?
  youtubeId         String?
  instagramId       String?
  twitterId         String?
  
  // Timestamps
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  lastLoginAt       DateTime?
  
  // Relations
  projects          Project[]
  videos            Video[]
  analytics         Analytics[]
  apiKeys           ApiKey[]
  subscriptions     Subscription[]
  
  @@map("users")
}

model Project {
  id          String        @id @default(cuid())
  name        String
  description String?
  templateId  String
  niche       String
  settings    Json
  status      ProjectStatus @default(DRAFT)
  
  // User relation
  userId      String
  user        User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // Timestamps
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  
  // Relations
  videos      Video[]
  
  @@map("projects")
}

model Video {
  id              String      @id @default(cuid())
  title           String
  description     String?
  script          String
  duration        Int
  fileUrl         String?
  thumbnailUrl    String?
  status          VideoStatus @default(PROCESSING)
  
  // Platform configurations
  platforms       Json
  aspectRatio     String      @default("9:16")
  resolution      String      @default("1080x1920")
  
  // AI settings
  voiceId         String
  voiceSettings   Json
  imagePrompts    Json
  effects         Json?
  
  // Publishing
  published       Boolean     @default(false)
  publishedAt     DateTime?
  scheduledFor    DateTime?
  
  // Relations
  userId          String
  user            User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  projectId       String
  project         Project     @relation(fields: [projectId], references: [id], onDelete: Cascade)
  
  // Timestamps
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt
  
  // Relations
  analytics       Analytics[]
  
  @@map("videos")
}

model Template {
  id          String   @id @default(cuid())
  name        String
  description String
  category    String
  niche       String
  preview     String?
  config      Json
  popularity  Int      @default(0)
  isActive    Boolean  @default(true)
  isPremium   Boolean  @default(false)
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("templates")
}

model Analytics {
  id            String   @id @default(cuid())
  platform      String
  views         Int      @default(0)
  likes         Int      @default(0)
  shares        Int      @default(0)
  comments      Int      @default(0)
  engagement    Float    @default(0)
  revenue       Float    @default(0)
  
  // Relations
  userId        String
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  videoId       String
  video         Video    @relation(fields: [videoId], references: [id], onDelete: Cascade)
  
  // Timestamps
  date          DateTime @default(now())
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  @@map("analytics")
}

model ApiKey {
  id          String   @id @default(cuid())
  name        String
  key         String   @unique
  permissions Json
  lastUsed    DateTime?
  isActive    Boolean  @default(true)
  
  // Relations
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("api_keys")
}

model Subscription {
  id              String           @id @default(cuid())
  stripeId        String           @unique
  tier            SubscriptionTier
  status          String
  currentPeriodStart DateTime
  currentPeriodEnd   DateTime
  cancelAtPeriodEnd  Boolean       @default(false)
  
  // Relations
  userId          String
  user            User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // Timestamps
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  
  @@map("subscriptions")
}

model RateLimit {
  id        String   @id @default(cuid())
  key       String   @unique
  count     Int      @default(1)
  resetTime DateTime
  
  @@map("rate_limits")
}

// Enums
enum SubscriptionTier {
  FREE
  STARTER
  PRO
  AGENCY
  ENTERPRISE
}

enum ProjectStatus {
  DRAFT
  ACTIVE
  COMPLETED
  ARCHIVED
}

enum VideoStatus {
  PROCESSING
  COMPLETED
  FAILED
  PUBLISHED
}
