{"name": "viralab", "version": "1.0.0", "description": "Advanced AI Video Generation Platform", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "build:netlify": "cd frontend && npm run build && cd ../backend && npm run build:serverless", "build:aws": "npm run build && cd deployment && serverless package", "build:gcp": "npm run build && docker build -t viralab .", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "start": "concurrently \"npm run start:frontend\" \"npm run start:backend\"", "start:frontend": "cd frontend && npm start", "start:backend": "cd backend && npm start", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm test", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "db:setup": "cd backend && npm run db:migrate && npm run db:seed", "db:migrate": "cd backend && npm run db:migrate", "db:seed": "cd backend && npm run db:seed", "deploy:netlify": "npm run build:netlify && netlify deploy --prod", "deploy:aws": "npm run build:aws && cd deployment && serverless deploy", "deploy:gcp": "npm run build:gcp && gcloud run deploy viralab --image gcr.io/PROJECT_ID/viralab", "deploy:vercel": "cd frontend && vercel --prod"}, "keywords": ["ai", "video", "generation", "viral", "content", "tiktok", "youtube", "instagram", "social-media"], "author": "HectorTa1989", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/viralab.git"}, "bugs": {"url": "https://github.com/HectorTa1989/viralab/issues"}, "homepage": "https://github.com/HectorTa1989/viralab#readme", "devDependencies": {"concurrently": "^8.2.2", "@types/node": "^20.10.0", "typescript": "^5.3.0", "eslint": "^8.55.0", "prettier": "^3.1.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "workspaces": ["frontend", "backend", "shared"]}