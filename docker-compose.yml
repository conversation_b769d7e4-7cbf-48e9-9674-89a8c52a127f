version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: viralab-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: viralab
      POSTGRES_USER: viralab
      POSTGRES_PASSWORD: viralab_password
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - viralab-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U viralab -d viralab"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: viralab-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - viralab-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Backend API
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: backend-builder
    container_name: viralab-backend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      DATABASE_URL: ***************************************************/viralab
      REDIS_URL: redis://:redis_password@redis:6379
      JWT_SECRET: dev-jwt-secret-change-in-production
      JWT_REFRESH_SECRET: dev-refresh-secret-change-in-production
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      ELEVENLABS_API_KEY: ${ELEVENLABS_API_KEY}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_S3_BUCKET: ${AWS_S3_BUCKET}
    volumes:
      - ./backend:/app/backend
      - /app/backend/node_modules
      - video_temp:/app/temp
    ports:
      - "3001:3001"
    networks:
      - viralab-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: npm run dev
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Application
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
      target: frontend-builder
    container_name: viralab-frontend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_API_URL: http://localhost:3001
      NEXT_PUBLIC_FRONTEND_URL: http://localhost:3000
    volumes:
      - ./frontend:/app/frontend
      - /app/frontend/node_modules
      - /app/frontend/.next
    ports:
      - "3000:3000"
    networks:
      - viralab-network
    depends_on:
      - backend
    command: npm run dev

  # Nginx Reverse Proxy (Production)
  nginx:
    image: nginx:alpine
    container_name: viralab-nginx
    restart: unless-stopped
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/ssl:/etc/nginx/ssl
    ports:
      - "80:80"
      - "443:443"
    networks:
      - viralab-network
    depends_on:
      - frontend
      - backend
    profiles:
      - production

  # Video Processing Worker
  worker:
    build:
      context: .
      dockerfile: Dockerfile
      target: backend-builder
    container_name: viralab-worker
    restart: unless-stopped
    environment:
      NODE_ENV: development
      DATABASE_URL: ***************************************************/viralab
      REDIS_URL: redis://:redis_password@redis:6379
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      ELEVENLABS_API_KEY: ${ELEVENLABS_API_KEY}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_S3_BUCKET: ${AWS_S3_BUCKET}
    volumes:
      - ./backend:/app/backend
      - /app/backend/node_modules
      - video_temp:/app/temp
    networks:
      - viralab-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: npm run worker
    profiles:
      - production

  # Monitoring with Prometheus (Optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: viralab-prometheus
    restart: unless-stopped
    volumes:
      - ./docker/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - viralab-network
    profiles:
      - monitoring

  # Grafana Dashboard (Optional)
  grafana:
    image: grafana/grafana:latest
    container_name: viralab-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
    ports:
      - "3003:3000"
    networks:
      - viralab-network
    profiles:
      - monitoring

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  video_temp:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  viralab-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
