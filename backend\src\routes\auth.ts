import express from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { body, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { authenticateToken, authenticateOptional } from '../middleware/auth';
import { rateLimiter } from '../middleware/rateLimiter';
import { logger, securityLogger } from '../utils/logger';
import { addToQueue } from '../services/queue';
import crypto from 'crypto';

const router = express.Router();
const prisma = new PrismaClient();

// JWT secret
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key';

// Token generation
const generateTokens = (userId: string) => {
  const accessToken = jwt.sign({ userId }, JWT_SECRET, { expiresIn: '15m' });
  const refreshToken = jwt.sign({ userId }, JWT_REFRESH_SECRET, { expiresIn: '7d' });
  return { accessToken, refreshToken };
};

// Register endpoint
router.post('/register', 
  rateLimiter('auth'),
  [
    body('email').isEmail().normalizeEmail(),
    body('password').isLength({ min: 8 }).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/),
    body('username').isLength({ min: 3, max: 30 }).matches(/^[a-zA-Z0-9_]+$/),
    body('firstName').optional().isLength({ min: 1, max: 50 }),
    body('lastName').optional().isLength({ min: 1, max: 50 }),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const { email, password, username, firstName, lastName } = req.body;

      // Check if user already exists
      const existingUser = await prisma.user.findFirst({
        where: {
          OR: [
            { email },
            { username },
          ],
        },
      });

      if (existingUser) {
        securityLogger.authFailure(email, req.ip, 'User already exists');
        return res.status(409).json({
          message: existingUser.email === email ? 'Email already registered' : 'Username already taken',
        });
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 12);

      // Generate email verification token
      const emailVerificationToken = crypto.randomBytes(32).toString('hex');

      // Create user
      const user = await prisma.user.create({
        data: {
          email,
          password: hashedPassword,
          username,
          firstName,
          lastName,
          emailVerificationToken,
          subscriptionTier: 'FREE',
          subscriptionStatus: 'ACTIVE',
        },
      });

      // Send verification email
      await addToQueue('email', 'send-verification-email', {
        userId: user.id,
        email: user.email,
        token: emailVerificationToken,
      });

      logger.info(`User registered successfully: ${email}`);

      res.status(201).json({
        message: 'Registration successful. Please check your email to verify your account.',
        userId: user.id,
      });

    } catch (error) {
      logger.error('Registration error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }
);

// Login endpoint
router.post('/login',
  rateLimiter('auth'),
  [
    body('email').isEmail().normalizeEmail(),
    body('password').notEmpty(),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const { email, password } = req.body;

      // Find user
      const user = await prisma.user.findUnique({
        where: { email },
      });

      if (!user) {
        securityLogger.authFailure(email, req.ip, 'User not found');
        return res.status(401).json({ message: 'Invalid credentials' });
      }

      // Check password
      const isValidPassword = await bcrypt.compare(password, user.password);
      if (!isValidPassword) {
        securityLogger.authFailure(email, req.ip, 'Invalid password');
        return res.status(401).json({ message: 'Invalid credentials' });
      }

      // Generate tokens
      const { accessToken, refreshToken } = generateTokens(user.id);

      // Store refresh token
      await prisma.user.update({
        where: { id: user.id },
        data: {
          refreshToken,
          lastLoginAt: new Date(),
        },
      });

      logger.info(`User logged in successfully: ${email}`);

      res.json({
        message: 'Login successful',
        accessToken,
        refreshToken,
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName,
          avatar: user.avatar,
          subscriptionTier: user.subscriptionTier,
          subscriptionStatus: user.subscriptionStatus,
          emailVerified: user.emailVerified,
          createdAt: user.createdAt,
        },
      });

    } catch (error) {
      logger.error('Login error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }
);

// Refresh token endpoint
router.post('/refresh',
  rateLimiter('auth'),
  [
    body('refreshToken').notEmpty(),
  ],
  async (req, res) => {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        return res.status(401).json({ message: 'Refresh token required' });
      }

      // Verify refresh token
      const decoded = jwt.verify(refreshToken, JWT_REFRESH_SECRET) as { userId: string };

      // Find user and validate refresh token
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
      });

      if (!user || user.refreshToken !== refreshToken) {
        return res.status(401).json({ message: 'Invalid refresh token' });
      }

      // Generate new tokens
      const tokens = generateTokens(user.id);

      // Update refresh token in database
      await prisma.user.update({
        where: { id: user.id },
        data: { refreshToken: tokens.refreshToken },
      });

      res.json(tokens);

    } catch (error) {
      logger.error('Token refresh error:', error);
      res.status(401).json({ message: 'Invalid refresh token' });
    }
  }
);

// Get current user
router.get('/me', authenticateToken, async (req, res) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        avatar: true,
        subscriptionTier: true,
        subscriptionStatus: true,
        emailVerified: true,
        createdAt: true,
        _count: {
          select: {
            videos: {
              where: {
                createdAt: {
                  gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
                },
              },
            },
          },
        },
      },
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({
      ...user,
      monthlyUsage: user._count.videos,
    });

  } catch (error) {
    logger.error('Get user error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Update profile
router.put('/profile',
  authenticateToken,
  [
    body('firstName').optional().isLength({ min: 1, max: 50 }),
    body('lastName').optional().isLength({ min: 1, max: 50 }),
    body('username').optional().isLength({ min: 3, max: 30 }).matches(/^[a-zA-Z0-9_]+$/),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const { firstName, lastName, username } = req.body;
      const updateData: any = {};

      if (firstName !== undefined) updateData.firstName = firstName;
      if (lastName !== undefined) updateData.lastName = lastName;
      if (username !== undefined) {
        // Check if username is already taken
        const existingUser = await prisma.user.findFirst({
          where: {
            username,
            NOT: { id: req.user.id },
          },
        });

        if (existingUser) {
          return res.status(409).json({ message: 'Username already taken' });
        }

        updateData.username = username;
      }

      const user = await prisma.user.update({
        where: { id: req.user.id },
        data: updateData,
        select: {
          id: true,
          email: true,
          username: true,
          firstName: true,
          lastName: true,
          avatar: true,
          subscriptionTier: true,
          subscriptionStatus: true,
          emailVerified: true,
          createdAt: true,
        },
      });

      logger.info(`Profile updated for user: ${req.user.id}`);

      res.json({
        message: 'Profile updated successfully',
        user,
      });

    } catch (error) {
      logger.error('Profile update error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }
);

// Change password
router.put('/password',
  authenticateToken,
  [
    body('currentPassword').notEmpty(),
    body('newPassword').isLength({ min: 8 }).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const { currentPassword, newPassword } = req.body;

      const user = await prisma.user.findUnique({
        where: { id: req.user.id },
      });

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Verify current password
      const isValidPassword = await bcrypt.compare(currentPassword, user.password);
      if (!isValidPassword) {
        securityLogger.authFailure(user.email, req.ip, 'Invalid current password');
        return res.status(401).json({ message: 'Current password is incorrect' });
      }

      // Hash new password
      const hashedNewPassword = await bcrypt.hash(newPassword, 12);

      // Update password
      await prisma.user.update({
        where: { id: req.user.id },
        data: { password: hashedNewPassword },
      });

      logger.info(`Password changed for user: ${req.user.id}`);

      res.json({ message: 'Password updated successfully' });

    } catch (error) {
      logger.error('Password change error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }
);

// Verify email
router.get('/verify-email/:token', async (req, res) => {
  try {
    const { token } = req.params;

    const user = await prisma.user.findFirst({
      where: { emailVerificationToken: token },
    });

    if (!user) {
      return res.status(400).json({ message: 'Invalid verification token' });
    }

    await prisma.user.update({
      where: { id: user.id },
      data: {
        emailVerified: true,
        emailVerificationToken: null,
      },
    });

    logger.info(`Email verified for user: ${user.email}`);

    res.json({ message: 'Email verified successfully' });

  } catch (error) {
    logger.error('Email verification error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Resend verification email
router.post('/resend-verification',
  authenticateToken,
  async (req, res) => {
    try {
      const user = await prisma.user.findUnique({
        where: { id: req.user.id },
      });

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      if (user.emailVerified) {
        return res.status(400).json({ message: 'Email already verified' });
      }

      // Generate new verification token
      const emailVerificationToken = crypto.randomBytes(32).toString('hex');

      await prisma.user.update({
        where: { id: user.id },
        data: { emailVerificationToken },
      });

      // Send verification email
      await addToQueue('email', 'send-verification-email', {
        userId: user.id,
        email: user.email,
        token: emailVerificationToken,
      });

      res.json({ message: 'Verification email sent' });

    } catch (error) {
      logger.error('Resend verification error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }
);

// Logout
router.post('/logout', authenticateToken, async (req, res) => {
  try {
    // Clear refresh token
    await prisma.user.update({
      where: { id: req.user.id },
      data: { refreshToken: null },
    });

    logger.info(`User logged out: ${req.user.id}`);

    res.json({ message: 'Logged out successfully' });

  } catch (error) {
    logger.error('Logout error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

export default router;
