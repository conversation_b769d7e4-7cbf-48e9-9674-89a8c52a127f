import express from 'express';
import { body, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { authenticateToken } from '../middleware/auth';
import { rateLimiter } from '../middleware/rateLimiter';
import { SubscriptionService, SUBSCRIPTION_TIERS } from '../services/subscription';
import { logger, businessLogger } from '../utils/logger';
import Stripe from 'stripe';

const router = express.Router();
const prisma = new PrismaClient();

const stripe = process.env.STRIPE_SECRET_KEY ? new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
}) : null;

// Get all subscription tiers
router.get('/tiers', async (req, res) => {
  try {
    res.json({
      tiers: SUBSCRIPTION_TIERS.map(tier => ({
        ...tier,
        stripePriceId: undefined, // Don't expose price IDs to frontend
      })),
    });
  } catch (error) {
    logger.error('Error fetching subscription tiers:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Get current user's subscription info
router.get('/current', authenticateToken, async (req, res) => {
  try {
    const usageStats = await SubscriptionService.getUsageStats(req.user.id);
    
    res.json({
      subscription: usageStats.currentTier,
      usage: usageStats.usage,
    });

  } catch (error) {
    logger.error('Error fetching subscription info:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Create checkout session
router.post('/checkout',
  authenticateToken,
  rateLimiter('general'),
  [
    body('tierId').isIn(['STARTER', 'PRO', 'AGENCY', 'ENTERPRISE']),
    body('successUrl').isURL(),
    body('cancelUrl').isURL(),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const { tierId, successUrl, cancelUrl } = req.body;

      // Check if user already has this tier or higher
      const user = await prisma.user.findUnique({
        where: { id: req.user.id },
      });

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      const currentTier = SubscriptionService.getTier(user.subscriptionTier);
      const targetTier = SubscriptionService.getTier(tierId);

      if (!targetTier) {
        return res.status(400).json({ message: 'Invalid subscription tier' });
      }

      if (currentTier && currentTier.price >= targetTier.price) {
        return res.status(400).json({ 
          message: 'You already have this tier or higher' 
        });
      }

      const session = await SubscriptionService.createCheckoutSession(
        req.user.id,
        tierId,
        successUrl,
        cancelUrl
      );

      businessLogger.subscriptionChanged(req.user.id, user.subscriptionTier, tierId);

      res.json({
        sessionId: session.sessionId,
        url: session.url,
      });

    } catch (error) {
      logger.error('Error creating checkout session:', error);
      res.status(500).json({ message: 'Failed to create checkout session' });
    }
  }
);

// Stripe webhook handler
router.post('/webhook',
  express.raw({ type: 'application/json' }),
  async (req, res) => {
    if (!stripe) {
      return res.status(400).json({ message: 'Stripe not configured' });
    }

    const sig = req.headers['stripe-signature'] as string;
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

    if (!webhookSecret) {
      logger.error('Stripe webhook secret not configured');
      return res.status(400).json({ message: 'Webhook secret not configured' });
    }

    let event: Stripe.Event;

    try {
      event = stripe.webhooks.constructEvent(req.body, sig, webhookSecret);
    } catch (err: any) {
      logger.error('Webhook signature verification failed:', err.message);
      return res.status(400).json({ message: 'Webhook signature verification failed' });
    }

    try {
      switch (event.type) {
        case 'checkout.session.completed':
          await handleCheckoutCompleted(event.data.object as Stripe.Checkout.Session);
          break;

        case 'customer.subscription.updated':
          await handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
          break;

        case 'customer.subscription.deleted':
          await handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
          break;

        case 'invoice.payment_succeeded':
          await handlePaymentSucceeded(event.data.object as Stripe.Invoice);
          break;

        case 'invoice.payment_failed':
          await handlePaymentFailed(event.data.object as Stripe.Invoice);
          break;

        default:
          logger.info(`Unhandled webhook event type: ${event.type}`);
      }

      res.json({ received: true });

    } catch (error) {
      logger.error('Webhook handler error:', error);
      res.status(500).json({ message: 'Webhook handler failed' });
    }
  }
);

// Cancel subscription
router.post('/cancel',
  authenticateToken,
  rateLimiter('general'),
  async (req, res) => {
    try {
      const user = await prisma.user.findUnique({
        where: { id: req.user.id },
      });

      if (!user || !user.stripeSubscriptionId) {
        return res.status(400).json({ message: 'No active subscription found' });
      }

      if (!stripe) {
        return res.status(400).json({ message: 'Stripe not configured' });
      }

      // Cancel subscription at period end
      await stripe.subscriptions.update(user.stripeSubscriptionId, {
        cancel_at_period_end: true,
      });

      logger.info(`Subscription cancellation scheduled for user: ${req.user.id}`);

      res.json({ message: 'Subscription will be cancelled at the end of the current period' });

    } catch (error) {
      logger.error('Error cancelling subscription:', error);
      res.status(500).json({ message: 'Failed to cancel subscription' });
    }
  }
);

// Reactivate subscription
router.post('/reactivate',
  authenticateToken,
  rateLimiter('general'),
  async (req, res) => {
    try {
      const user = await prisma.user.findUnique({
        where: { id: req.user.id },
      });

      if (!user || !user.stripeSubscriptionId) {
        return res.status(400).json({ message: 'No subscription found' });
      }

      if (!stripe) {
        return res.status(400).json({ message: 'Stripe not configured' });
      }

      // Reactivate subscription
      await stripe.subscriptions.update(user.stripeSubscriptionId, {
        cancel_at_period_end: false,
      });

      await prisma.user.update({
        where: { id: req.user.id },
        data: {
          subscriptionStatus: 'ACTIVE',
        },
      });

      logger.info(`Subscription reactivated for user: ${req.user.id}`);

      res.json({ message: 'Subscription reactivated successfully' });

    } catch (error) {
      logger.error('Error reactivating subscription:', error);
      res.status(500).json({ message: 'Failed to reactivate subscription' });
    }
  }
);

// Get billing history
router.get('/billing-history',
  authenticateToken,
  async (req, res) => {
    try {
      const user = await prisma.user.findUnique({
        where: { id: req.user.id },
      });

      if (!user || !user.stripeCustomerId || !stripe) {
        return res.json({ invoices: [] });
      }

      const invoices = await stripe.invoices.list({
        customer: user.stripeCustomerId,
        limit: 20,
      });

      const formattedInvoices = invoices.data.map(invoice => ({
        id: invoice.id,
        amount: invoice.amount_paid / 100, // Convert from cents
        currency: invoice.currency.toUpperCase(),
        status: invoice.status,
        date: new Date(invoice.created * 1000),
        invoiceUrl: invoice.hosted_invoice_url,
        pdfUrl: invoice.invoice_pdf,
      }));

      res.json({ invoices: formattedInvoices });

    } catch (error) {
      logger.error('Error fetching billing history:', error);
      res.status(500).json({ message: 'Failed to fetch billing history' });
    }
  }
);

// Webhook event handlers
async function handleCheckoutCompleted(session: Stripe.Checkout.Session) {
  const { userId, tierId } = session.metadata || {};
  
  if (!userId || !tierId) {
    logger.error('Missing metadata in checkout session');
    return;
  }

  if (session.subscription && typeof session.subscription === 'string') {
    await SubscriptionService.handleSubscriptionSuccess(
      userId,
      session.subscription,
      tierId
    );
  }
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  // Handle subscription updates (e.g., plan changes)
  const customer = subscription.customer as string;
  
  const user = await prisma.user.findFirst({
    where: { stripeCustomerId: customer },
  });

  if (user) {
    await prisma.user.update({
      where: { id: user.id },
      data: {
        subscriptionStatus: subscription.status === 'active' ? 'ACTIVE' : 'CANCELLED',
      },
    });
  }
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  const customer = subscription.customer as string;
  
  const user = await prisma.user.findFirst({
    where: { stripeCustomerId: customer },
  });

  if (user) {
    await SubscriptionService.handleSubscriptionCancellation(user.id);
  }
}

async function handlePaymentSucceeded(invoice: Stripe.Invoice) {
  const customer = invoice.customer as string;
  
  const user = await prisma.user.findFirst({
    where: { stripeCustomerId: customer },
  });

  if (user && invoice.amount_paid) {
    businessLogger.paymentProcessed(
      user.id,
      invoice.amount_paid / 100,
      invoice.currency.toUpperCase(),
      'succeeded'
    );
  }
}

async function handlePaymentFailed(invoice: Stripe.Invoice) {
  const customer = invoice.customer as string;
  
  const user = await prisma.user.findFirst({
    where: { stripeCustomerId: customer },
  });

  if (user) {
    businessLogger.paymentProcessed(
      user.id,
      invoice.amount_due ? invoice.amount_due / 100 : 0,
      invoice.currency.toUpperCase(),
      'failed'
    );

    // Optionally downgrade user or send notification
    // This depends on your business logic
  }
}

export default router;
