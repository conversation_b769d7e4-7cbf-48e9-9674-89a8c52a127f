import axios from 'axios';
import { HfInference } from '@huggingface/inference';
import OpenAI from 'openai';
import { logger } from '../utils/logger';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Initialize AI services
const hf = new HfInference(process.env.HUGGINGFACE_API_KEY);
const openai = process.env.OPENAI_API_KEY ? new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
}) : null;

export interface AIServiceConfig {
  preferFree: boolean;
  maxRetries: number;
  fallbackToPremium: boolean;
  userId: string;
  subscriptionTier: string;
}

export interface TextGenerationResult {
  text: string;
  model: string;
  cost: number;
  processingTime: number;
  quality: 'low' | 'medium' | 'high';
}

export interface VoiceGenerationResult {
  audioBuffer: Buffer;
  model: string;
  cost: number;
  processingTime: number;
  quality: 'low' | 'medium' | 'high';
}

export interface ImageGenerationResult {
  imageBuffer: Buffer;
  model: string;
  cost: number;
  processingTime: number;
  quality: 'low' | 'medium' | 'high';
}

export class AIServices {
  private config: AIServiceConfig;

  constructor(config: AIServiceConfig) {
    this.config = config;
  }

  // Text Generation with Free/Premium Fallback
  async generateText(prompt: string, options: {
    maxTokens?: number;
    temperature?: number;
    model?: string;
  } = {}): Promise<TextGenerationResult> {
    const startTime = Date.now();
    
    try {
      // Try free models first if preferred
      if (this.config.preferFree || this.config.subscriptionTier === 'FREE') {
        try {
          const result = await this.generateTextFree(prompt, options);
          await this.logUsage('text_generation', 'free', result.model, result.cost);
          return result;
        } catch (error) {
          logger.warn('Free text generation failed, trying premium fallback:', error);
          
          if (!this.config.fallbackToPremium) {
            throw error;
          }
        }
      }

      // Fallback to premium models
      if (openai && (this.config.subscriptionTier !== 'FREE' || this.config.fallbackToPremium)) {
        const result = await this.generateTextPremium(prompt, options);
        await this.logUsage('text_generation', 'premium', result.model, result.cost);
        return result;
      }

      throw new Error('No available text generation service');

    } catch (error) {
      logger.error('Text generation failed:', error);
      throw new Error('Failed to generate text content');
    }
  }

  // Free Text Generation using Hugging Face
  private async generateTextFree(prompt: string, options: any): Promise<TextGenerationResult> {
    const startTime = Date.now();
    
    try {
      // Try multiple free models in order of preference
      const models = [
        'microsoft/DialoGPT-large',
        'google/flan-t5-large',
        'facebook/blenderbot-400M-distill',
      ];

      for (const model of models) {
        try {
          const response = await hf.textGeneration({
            model,
            inputs: prompt,
            parameters: {
              max_new_tokens: options.maxTokens || 150,
              temperature: options.temperature || 0.8,
              do_sample: true,
              top_p: 0.9,
            },
          });

          const processingTime = Date.now() - startTime;
          
          return {
            text: response.generated_text || '',
            model: `hf:${model}`,
            cost: 0, // Free tier
            processingTime,
            quality: 'medium',
          };
        } catch (modelError) {
          logger.warn(`Model ${model} failed, trying next:`, modelError);
          continue;
        }
      }

      throw new Error('All free text models failed');

    } catch (error) {
      logger.error('Free text generation failed:', error);
      throw error;
    }
  }

  // Premium Text Generation using OpenAI
  private async generateTextPremium(prompt: string, options: any): Promise<TextGenerationResult> {
    const startTime = Date.now();
    
    if (!openai) {
      throw new Error('OpenAI not configured');
    }

    try {
      const response = await openai.chat.completions.create({
        model: options.model || 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a creative content writer specializing in viral social media content.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        max_tokens: options.maxTokens || 150,
        temperature: options.temperature || 0.8,
      });

      const processingTime = Date.now() - startTime;
      const cost = this.calculateOpenAICost(response.usage?.total_tokens || 0, options.model || 'gpt-3.5-turbo');

      return {
        text: response.choices[0]?.message?.content || '',
        model: `openai:${options.model || 'gpt-3.5-turbo'}`,
        cost,
        processingTime,
        quality: 'high',
      };

    } catch (error) {
      logger.error('Premium text generation failed:', error);
      throw error;
    }
  }

  // Voice Generation with Free/Premium Fallback
  async generateVoice(text: string, options: {
    voiceId?: string;
    speed?: number;
    pitch?: number;
    emotion?: string;
  } = {}): Promise<VoiceGenerationResult> {
    const startTime = Date.now();

    try {
      // Try free TTS first
      if (this.config.preferFree || this.config.subscriptionTier === 'FREE') {
        try {
          const result = await this.generateVoiceFree(text, options);
          await this.logUsage('voice_generation', 'free', result.model, result.cost);
          return result;
        } catch (error) {
          logger.warn('Free voice generation failed, trying premium fallback:', error);
          
          if (!this.config.fallbackToPremium) {
            throw error;
          }
        }
      }

      // Fallback to premium voice services
      if (process.env.ELEVENLABS_API_KEY && (this.config.subscriptionTier !== 'FREE' || this.config.fallbackToPremium)) {
        const result = await this.generateVoicePremium(text, options);
        await this.logUsage('voice_generation', 'premium', result.model, result.cost);
        return result;
      }

      throw new Error('No available voice generation service');

    } catch (error) {
      logger.error('Voice generation failed:', error);
      throw new Error('Failed to generate voice audio');
    }
  }

  // Free Voice Generation using Mozilla TTS or similar
  private async generateVoiceFree(text: string, options: any): Promise<VoiceGenerationResult> {
    const startTime = Date.now();

    try {
      // Use Hugging Face TTS models (free tier)
      const response = await hf.textToSpeech({
        model: 'microsoft/speecht5_tts',
        inputs: text,
      });

      const processingTime = Date.now() - startTime;
      const audioBuffer = Buffer.from(await response.arrayBuffer());

      return {
        audioBuffer,
        model: 'hf:microsoft/speecht5_tts',
        cost: 0,
        processingTime,
        quality: 'medium',
      };

    } catch (error) {
      // Fallback to system TTS or other free service
      logger.warn('Hugging Face TTS failed, using system TTS:', error);
      
      // Create a simple beep sound as fallback (placeholder)
      const audioBuffer = await this.generateSilentAudio(5); // 5 seconds of silence
      
      return {
        audioBuffer,
        model: 'system:silent',
        cost: 0,
        processingTime: Date.now() - startTime,
        quality: 'low',
      };
    }
  }

  // Premium Voice Generation using ElevenLabs
  private async generateVoicePremium(text: string, options: any): Promise<VoiceGenerationResult> {
    const startTime = Date.now();

    try {
      const response = await axios.post(
        `https://api.elevenlabs.io/v1/text-to-speech/${options.voiceId || '21m00Tcm4TlvDq8ikWAM'}`,
        {
          text,
          voice_settings: {
            stability: 0.5,
            similarity_boost: 0.5,
            speed: options.speed || 1.0,
          },
        },
        {
          headers: {
            'xi-api-key': process.env.ELEVENLABS_API_KEY,
            'Content-Type': 'application/json',
          },
          responseType: 'arraybuffer',
        }
      );

      const processingTime = Date.now() - startTime;
      const cost = this.calculateElevenLabsCost(text.length);

      return {
        audioBuffer: Buffer.from(response.data),
        model: 'elevenlabs:premium',
        cost,
        processingTime,
        quality: 'high',
      };

    } catch (error) {
      logger.error('ElevenLabs voice generation failed:', error);
      throw error;
    }
  }

  // Image Generation with Free/Premium Fallback
  async generateImage(prompt: string, options: {
    width?: number;
    height?: number;
    style?: string;
  } = {}): Promise<ImageGenerationResult> {
    const startTime = Date.now();

    try {
      // Try free image generation first
      if (this.config.preferFree || this.config.subscriptionTier === 'FREE') {
        try {
          const result = await this.generateImageFree(prompt, options);
          await this.logUsage('image_generation', 'free', result.model, result.cost);
          return result;
        } catch (error) {
          logger.warn('Free image generation failed, trying premium fallback:', error);
          
          if (!this.config.fallbackToPremium) {
            throw error;
          }
        }
      }

      // Fallback to premium image services
      if (openai && (this.config.subscriptionTier !== 'FREE' || this.config.fallbackToPremium)) {
        const result = await this.generateImagePremium(prompt, options);
        await this.logUsage('image_generation', 'premium', result.model, result.cost);
        return result;
      }

      throw new Error('No available image generation service');

    } catch (error) {
      logger.error('Image generation failed:', error);
      throw new Error('Failed to generate image');
    }
  }

  // Free Image Generation using Stable Diffusion
  private async generateImageFree(prompt: string, options: any): Promise<ImageGenerationResult> {
    const startTime = Date.now();

    try {
      // Use Hugging Face Stable Diffusion (free tier)
      const response = await hf.textToImage({
        model: 'stabilityai/stable-diffusion-2-1',
        inputs: prompt,
        parameters: {
          width: options.width || 512,
          height: options.height || 512,
        },
      });

      const processingTime = Date.now() - startTime;
      const imageBuffer = Buffer.from(await response.arrayBuffer());

      return {
        imageBuffer,
        model: 'hf:stabilityai/stable-diffusion-2-1',
        cost: 0,
        processingTime,
        quality: 'medium',
      };

    } catch (error) {
      logger.error('Free image generation failed:', error);
      throw error;
    }
  }

  // Premium Image Generation using DALL-E
  private async generateImagePremium(prompt: string, options: any): Promise<ImageGenerationResult> {
    const startTime = Date.now();

    if (!openai) {
      throw new Error('OpenAI not configured');
    }

    try {
      const response = await openai.images.generate({
        model: 'dall-e-3',
        prompt,
        size: '1024x1024',
        quality: 'standard',
        n: 1,
      });

      const imageUrl = response.data[0]?.url;
      if (!imageUrl) {
        throw new Error('No image URL returned');
      }

      const imageResponse = await axios.get(imageUrl, { responseType: 'arraybuffer' });
      const processingTime = Date.now() - startTime;
      const cost = 0.04; // DALL-E 3 standard cost

      return {
        imageBuffer: Buffer.from(imageResponse.data),
        model: 'openai:dall-e-3',
        cost,
        processingTime,
        quality: 'high',
      };

    } catch (error) {
      logger.error('Premium image generation failed:', error);
      throw error;
    }
  }

  // Utility Functions
  private async generateSilentAudio(durationSeconds: number): Promise<Buffer> {
    // Generate silent audio buffer (placeholder implementation)
    const sampleRate = 44100;
    const samples = sampleRate * durationSeconds;
    const buffer = Buffer.alloc(samples * 2); // 16-bit audio
    return buffer;
  }

  private calculateOpenAICost(tokens: number, model: string): number {
    const rates: { [key: string]: number } = {
      'gpt-3.5-turbo': 0.002 / 1000, // $0.002 per 1K tokens
      'gpt-4': 0.03 / 1000, // $0.03 per 1K tokens
    };
    return (rates[model] || rates['gpt-3.5-turbo']) * tokens;
  }

  private calculateElevenLabsCost(textLength: number): number {
    // ElevenLabs charges per character
    return (textLength / 1000) * 0.30; // $0.30 per 1K characters
  }

  private async logUsage(service: string, tier: 'free' | 'premium', model: string, cost: number): Promise<void> {
    try {
      await prisma.aPIUsage.create({
        data: {
          userId: this.config.userId,
          service,
          tier,
          model,
          cost,
          timestamp: new Date(),
        },
      });
    } catch (error) {
      logger.error('Failed to log API usage:', error);
    }
  }
}
