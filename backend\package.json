{"name": "viralab-backend", "version": "1.0.0", "description": "ViraLab Backend API", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc && tsc-alias", "build:serverless": "tsc && serverless package", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:seed": "npx prisma db seed", "db:studio": "npx prisma studio", "db:reset": "npx prisma migrate reset"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-slow-down": "^2.0.1", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "dotenv": "^16.3.1", "prisma": "^5.7.0", "@prisma/client": "^5.7.0", "redis": "^4.6.11", "ioredis": "^5.3.2", "bull": "^4.12.0", "multer": "^1.4.5-lts.1", "sharp": "^0.33.0", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.2", "aws-sdk": "^2.1506.0", "@aws-sdk/client-s3": "^3.462.0", "@aws-sdk/s3-request-presigner": "^3.462.0", "openai": "^4.20.1", "axios": "^1.6.2", "node-cron": "^3.0.3", "winston": "^3.11.0", "morgan": "^1.10.0", "cookie-parser": "^1.4.6", "express-session": "^1.17.3", "connect-redis": "^7.1.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-google-oauth20": "^2.0.0", "nodemailer": "^6.9.7", "stripe": "^14.9.0", "joi": "^17.11.0", "uuid": "^9.0.1", "moment": "^2.29.4", "lodash": "^4.17.21", "crypto-js": "^4.2.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/multer": "^1.4.11", "@types/fluent-ffmpeg": "^2.1.24", "@types/node": "^20.10.0", "@types/node-cron": "^3.0.11", "@types/morgan": "^1.9.9", "@types/cookie-parser": "^1.4.6", "@types/express-session": "^1.17.10", "@types/passport": "^1.0.16", "@types/passport-jwt": "^3.0.13", "@types/passport-google-oauth20": "^2.0.14", "@types/nodemailer": "^6.4.14", "@types/joi": "^17.2.3", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "@types/crypto-js": "^4.2.1", "typescript": "^5.3.2", "tsc-alias": "^1.8.8", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "prettier": "^3.1.0", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "@types/supertest": "^2.0.16", "serverless": "^3.38.0", "serverless-offline": "^13.3.0"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}