import Stripe from 'stripe';
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import { addToQueue } from './queue';

const prisma = new PrismaClient();
const stripe = process.env.STRIPE_SECRET_KEY ? new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
}) : null;

export interface SubscriptionTier {
  id: string;
  name: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: {
    videosPerMonth: number;
    maxDuration: number;
    platforms: string[];
    aiVoices: boolean;
    customBranding: boolean;
    analytics: boolean;
    apiAccess: boolean;
    priority: 'low' | 'medium' | 'high';
  };
  stripePriceId?: string;
}

export const SUBSCRIPTION_TIERS: SubscriptionTier[] = [
  {
    id: 'FREE',
    name: 'Free',
    price: 0,
    currency: 'USD',
    interval: 'month',
    features: {
      videosPerMonth: 5,
      maxDuration: 30,
      platforms: ['tiktok'],
      aiVoices: false,
      customBranding: false,
      analytics: false,
      apiAccess: false,
      priority: 'low',
    },
  },
  {
    id: 'STARTER',
    name: 'Starter',
    price: 19,
    currency: 'USD',
    interval: 'month',
    stripePriceId: process.env.STRIPE_STARTER_PRICE_ID,
    features: {
      videosPerMonth: 50,
      maxDuration: 60,
      platforms: ['tiktok', 'youtube', 'instagram'],
      aiVoices: true,
      customBranding: false,
      analytics: true,
      apiAccess: false,
      priority: 'medium',
    },
  },
  {
    id: 'PRO',
    name: 'Pro',
    price: 49,
    currency: 'USD',
    interval: 'month',
    stripePriceId: process.env.STRIPE_PRO_PRICE_ID,
    features: {
      videosPerMonth: 200,
      maxDuration: 180,
      platforms: ['tiktok', 'youtube', 'instagram', 'twitter'],
      aiVoices: true,
      customBranding: true,
      analytics: true,
      apiAccess: true,
      priority: 'high',
    },
  },
  {
    id: 'AGENCY',
    name: 'Agency',
    price: 149,
    currency: 'USD',
    interval: 'month',
    stripePriceId: process.env.STRIPE_AGENCY_PRICE_ID,
    features: {
      videosPerMonth: 1000,
      maxDuration: 300,
      platforms: ['tiktok', 'youtube', 'instagram', 'twitter'],
      aiVoices: true,
      customBranding: true,
      analytics: true,
      apiAccess: true,
      priority: 'high',
    },
  },
  {
    id: 'ENTERPRISE',
    name: 'Enterprise',
    price: 499,
    currency: 'USD',
    interval: 'month',
    stripePriceId: process.env.STRIPE_ENTERPRISE_PRICE_ID,
    features: {
      videosPerMonth: -1, // Unlimited
      maxDuration: 600,
      platforms: ['tiktok', 'youtube', 'instagram', 'twitter'],
      aiVoices: true,
      customBranding: true,
      analytics: true,
      apiAccess: true,
      priority: 'high',
    },
  },
];

export class SubscriptionService {
  // Get subscription tier details
  static getTier(tierId: string): SubscriptionTier | null {
    return SUBSCRIPTION_TIERS.find(tier => tier.id === tierId) || null;
  }

  // Check if user can perform action based on subscription
  static async canPerformAction(
    userId: string,
    action: 'generate_video' | 'use_ai_voice' | 'custom_branding' | 'api_access',
    additionalData?: any
  ): Promise<{ allowed: boolean; reason?: string; upgradeRequired?: string }> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          subscription: true,
        },
      });

      if (!user) {
        return { allowed: false, reason: 'User not found' };
      }

      const tier = this.getTier(user.subscriptionTier);
      if (!tier) {
        return { allowed: false, reason: 'Invalid subscription tier' };
      }

      switch (action) {
        case 'generate_video':
          return await this.checkVideoGenerationLimit(user, tier);
        
        case 'use_ai_voice':
          return {
            allowed: tier.features.aiVoices,
            reason: tier.features.aiVoices ? undefined : 'AI voices not available in your plan',
            upgradeRequired: tier.features.aiVoices ? undefined : 'STARTER',
          };
        
        case 'custom_branding':
          return {
            allowed: tier.features.customBranding,
            reason: tier.features.customBranding ? undefined : 'Custom branding not available in your plan',
            upgradeRequired: tier.features.customBranding ? undefined : 'PRO',
          };
        
        case 'api_access':
          return {
            allowed: tier.features.apiAccess,
            reason: tier.features.apiAccess ? undefined : 'API access not available in your plan',
            upgradeRequired: tier.features.apiAccess ? undefined : 'PRO',
          };
        
        default:
          return { allowed: false, reason: 'Unknown action' };
      }

    } catch (error) {
      logger.error('Error checking subscription permissions:', error);
      return { allowed: false, reason: 'Internal error' };
    }
  }

  // Check video generation limits
  private static async checkVideoGenerationLimit(user: any, tier: SubscriptionTier) {
    if (tier.features.videosPerMonth === -1) {
      return { allowed: true }; // Unlimited
    }

    const currentMonth = new Date();
    currentMonth.setDate(1);
    currentMonth.setHours(0, 0, 0, 0);

    const videosThisMonth = await prisma.video.count({
      where: {
        userId: user.id,
        createdAt: {
          gte: currentMonth,
        },
      },
    });

    const allowed = videosThisMonth < tier.features.videosPerMonth;
    
    return {
      allowed,
      reason: allowed ? undefined : `Monthly video limit reached (${tier.features.videosPerMonth})`,
      upgradeRequired: allowed ? undefined : this.getNextTier(tier.id),
    };
  }

  // Get next tier recommendation
  private static getNextTier(currentTier: string): string {
    const tierOrder = ['FREE', 'STARTER', 'PRO', 'AGENCY', 'ENTERPRISE'];
    const currentIndex = tierOrder.indexOf(currentTier);
    return currentIndex < tierOrder.length - 1 ? tierOrder[currentIndex + 1] : 'ENTERPRISE';
  }

  // Create Stripe checkout session
  static async createCheckoutSession(
    userId: string,
    tierId: string,
    successUrl: string,
    cancelUrl: string
  ): Promise<{ sessionId: string; url: string }> {
    if (!stripe) {
      throw new Error('Stripe not configured');
    }

    const tier = this.getTier(tierId);
    if (!tier || !tier.stripePriceId) {
      throw new Error('Invalid subscription tier');
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new Error('User not found');
    }

    try {
      const session = await stripe.checkout.sessions.create({
        customer_email: user.email,
        payment_method_types: ['card'],
        line_items: [
          {
            price: tier.stripePriceId,
            quantity: 1,
          },
        ],
        mode: 'subscription',
        success_url: successUrl,
        cancel_url: cancelUrl,
        metadata: {
          userId,
          tierId,
        },
      });

      return {
        sessionId: session.id,
        url: session.url!,
      };

    } catch (error) {
      logger.error('Error creating Stripe checkout session:', error);
      throw new Error('Failed to create checkout session');
    }
  }

  // Handle successful subscription
  static async handleSubscriptionSuccess(
    userId: string,
    stripeSubscriptionId: string,
    tierId: string
  ): Promise<void> {
    try {
      // Update user subscription
      await prisma.user.update({
        where: { id: userId },
        data: {
          subscriptionTier: tierId,
          stripeSubscriptionId,
          subscriptionStatus: 'ACTIVE',
          subscriptionUpdatedAt: new Date(),
        },
      });

      // Create subscription record
      await prisma.subscription.create({
        data: {
          userId,
          tierId,
          stripeSubscriptionId,
          status: 'ACTIVE',
          startDate: new Date(),
        },
      });

      // Send welcome email
      await addToQueue('email', 'send-email', {
        to: (await prisma.user.findUnique({ where: { id: userId } }))?.email,
        subject: 'Welcome to ViraLab Pro!',
        template: 'subscription-welcome',
        data: { tierId },
      });

      // Track analytics
      await addToQueue('analytics', 'track-event', {
        userId,
        event: 'subscription_upgraded',
        data: { tierId, stripeSubscriptionId },
      });

      logger.info(`Subscription activated for user ${userId}: ${tierId}`);

    } catch (error) {
      logger.error('Error handling subscription success:', error);
      throw error;
    }
  }

  // Handle subscription cancellation
  static async handleSubscriptionCancellation(userId: string): Promise<void> {
    try {
      await prisma.user.update({
        where: { id: userId },
        data: {
          subscriptionTier: 'FREE',
          subscriptionStatus: 'CANCELLED',
          subscriptionUpdatedAt: new Date(),
        },
      });

      await prisma.subscription.updateMany({
        where: { userId, status: 'ACTIVE' },
        data: { status: 'CANCELLED', endDate: new Date() },
      });

      // Send cancellation email
      await addToQueue('email', 'send-email', {
        to: (await prisma.user.findUnique({ where: { id: userId } }))?.email,
        subject: 'Subscription Cancelled',
        template: 'subscription-cancelled',
        data: { userId },
      });

      logger.info(`Subscription cancelled for user ${userId}`);

    } catch (error) {
      logger.error('Error handling subscription cancellation:', error);
      throw error;
    }
  }

  // Get usage statistics
  static async getUsageStats(userId: string): Promise<{
    currentTier: SubscriptionTier;
    usage: {
      videosThisMonth: number;
      videosLimit: number;
      apiCallsThisMonth: number;
    };
  }> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new Error('User not found');
    }

    const tier = this.getTier(user.subscriptionTier)!;
    
    const currentMonth = new Date();
    currentMonth.setDate(1);
    currentMonth.setHours(0, 0, 0, 0);

    const [videosThisMonth, apiCallsThisMonth] = await Promise.all([
      prisma.video.count({
        where: { userId, createdAt: { gte: currentMonth } },
      }),
      prisma.aPIUsage.count({
        where: { userId, timestamp: { gte: currentMonth } },
      }),
    ]);

    return {
      currentTier: tier,
      usage: {
        videosThisMonth,
        videosLimit: tier.features.videosPerMonth,
        apiCallsThisMonth,
      },
    };
  }
}
