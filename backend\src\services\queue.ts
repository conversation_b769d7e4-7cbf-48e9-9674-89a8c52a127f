import Bull from 'bull';
import Redis from 'ioredis';
import { logger } from '../utils/logger';
import { VideoGenerationService } from './videoGeneration';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Redis connection
const redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379', {
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  lazyConnect: true,
});

// Queue configurations
const queueConfig = {
  redis: {
    port: parseInt(process.env.REDIS_PORT || '6379'),
    host: process.env.REDIS_HOST || 'localhost',
    password: process.env.REDIS_PASSWORD,
  },
  defaultJobOptions: {
    removeOnComplete: 10,
    removeOnFail: 5,
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
  },
};

// Initialize queues
export const videoGenerationQueue = new Bull('video-generation', queueConfig);
export const emailQueue = new Bull('email', queueConfig);
export const analyticsQueue = new Bull('analytics', queueConfig);
export const publishingQueue = new Bull('publishing', queueConfig);

// Video Generation Queue Processor
videoGenerationQueue.process('generate-video', 5, async (job) => {
  const { videoId, userId, config } = job.data;
  
  try {
    logger.info(`Processing video generation job: ${job.id}`, { videoId, userId });
    
    // Update job progress
    await job.progress(10);
    
    // Initialize video generation service
    const videoService = new VideoGenerationService();
    
    // Update progress
    await job.progress(20);
    
    // Process video generation
    await videoService.processVideoGeneration({ videoId, userId, config });
    
    // Update progress
    await job.progress(100);
    
    logger.info(`Video generation completed: ${videoId}`);
    
    // Add analytics job
    await addToQueue('analytics', 'track-video-generation', {
      userId,
      videoId,
      timestamp: new Date(),
    });
    
    return { success: true, videoId };
    
  } catch (error) {
    logger.error(`Video generation failed for job ${job.id}:`, error);
    
    // Update video status to failed
    await prisma.video.update({
      where: { id: videoId },
      data: { status: 'FAILED' },
    });
    
    throw error;
  }
});

// Email Queue Processor
emailQueue.process('send-email', 10, async (job) => {
  const { to, subject, template, data } = job.data;
  
  try {
    logger.info(`Processing email job: ${job.id}`, { to, subject });
    
    // TODO: Implement email sending logic
    // This could use SendGrid, AWS SES, or other email service
    
    await job.progress(50);
    
    // Simulate email sending
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await job.progress(100);
    
    logger.info(`Email sent successfully: ${to}`);
    
    return { success: true, to };
    
  } catch (error) {
    logger.error(`Email sending failed for job ${job.id}:`, error);
    throw error;
  }
});

// Analytics Queue Processor
analyticsQueue.process('track-event', 20, async (job) => {
  const { userId, event, data, timestamp } = job.data;
  
  try {
    logger.info(`Processing analytics job: ${job.id}`, { userId, event });
    
    // Store analytics event
    await prisma.analyticsEvent.create({
      data: {
        userId,
        event,
        data: data || {},
        timestamp: timestamp || new Date(),
      },
    });
    
    await job.progress(100);
    
    return { success: true, event };
    
  } catch (error) {
    logger.error(`Analytics tracking failed for job ${job.id}:`, error);
    throw error;
  }
});

// Publishing Queue Processor
publishingQueue.process('publish-video', 3, async (job) => {
  const { videoId, platforms, userId } = job.data;
  
  try {
    logger.info(`Processing publishing job: ${job.id}`, { videoId, platforms });
    
    const video = await prisma.video.findUnique({
      where: { id: videoId },
      include: { user: true },
    });
    
    if (!video) {
      throw new Error('Video not found');
    }
    
    await job.progress(20);
    
    // TODO: Implement platform-specific publishing logic
    for (const platform of platforms) {
      try {
        await publishToPlatform(video, platform);
        await job.progress(20 + (platforms.indexOf(platform) + 1) * (60 / platforms.length));
      } catch (platformError) {
        logger.error(`Failed to publish to ${platform}:`, platformError);
        // Continue with other platforms
      }
    }
    
    // Update video status
    await prisma.video.update({
      where: { id: videoId },
      data: {
        published: true,
        publishedAt: new Date(),
      },
    });
    
    await job.progress(100);
    
    logger.info(`Video published successfully: ${videoId}`);
    
    return { success: true, videoId, platforms };
    
  } catch (error) {
    logger.error(`Video publishing failed for job ${job.id}:`, error);
    throw error;
  }
});

// Helper function to publish to specific platforms
async function publishToPlatform(video: any, platform: string): Promise<void> {
  switch (platform.toLowerCase()) {
    case 'tiktok':
      // TODO: Implement TikTok API integration
      logger.info(`Publishing to TikTok: ${video.id}`);
      break;
      
    case 'youtube':
      // TODO: Implement YouTube API integration
      logger.info(`Publishing to YouTube: ${video.id}`);
      break;
      
    case 'instagram':
      // TODO: Implement Instagram API integration
      logger.info(`Publishing to Instagram: ${video.id}`);
      break;
      
    case 'twitter':
      // TODO: Implement Twitter API integration
      logger.info(`Publishing to Twitter: ${video.id}`);
      break;
      
    default:
      throw new Error(`Unsupported platform: ${platform}`);
  }
  
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 2000));
}

// Queue event handlers
videoGenerationQueue.on('completed', (job, result) => {
  logger.info(`Video generation job completed: ${job.id}`, result);
});

videoGenerationQueue.on('failed', (job, err) => {
  logger.error(`Video generation job failed: ${job.id}`, err);
});

emailQueue.on('completed', (job, result) => {
  logger.info(`Email job completed: ${job.id}`, result);
});

analyticsQueue.on('completed', (job, result) => {
  logger.info(`Analytics job completed: ${job.id}`, result);
});

publishingQueue.on('completed', (job, result) => {
  logger.info(`Publishing job completed: ${job.id}`, result);
});

// Utility function to add jobs to queues
export async function addToQueue(
  queueName: string,
  jobType: string,
  data: any,
  options: any = {}
): Promise<Bull.Job> {
  let queue: Bull.Queue;
  
  switch (queueName) {
    case 'video-generation':
      queue = videoGenerationQueue;
      jobType = 'generate-video';
      break;
    case 'email':
      queue = emailQueue;
      jobType = 'send-email';
      break;
    case 'analytics':
      queue = analyticsQueue;
      jobType = 'track-event';
      break;
    case 'publishing':
      queue = publishingQueue;
      jobType = 'publish-video';
      break;
    default:
      throw new Error(`Unknown queue: ${queueName}`);
  }
  
  const job = await queue.add(jobType, data, {
    ...queueConfig.defaultJobOptions,
    ...options,
  });
  
  logger.info(`Job added to ${queueName} queue: ${job.id}`, { jobType, data });
  
  return job;
}

// Queue monitoring and health check
export async function getQueueStats() {
  const stats = {
    videoGeneration: {
      waiting: await videoGenerationQueue.getWaiting(),
      active: await videoGenerationQueue.getActive(),
      completed: await videoGenerationQueue.getCompleted(),
      failed: await videoGenerationQueue.getFailed(),
    },
    email: {
      waiting: await emailQueue.getWaiting(),
      active: await emailQueue.getActive(),
      completed: await emailQueue.getCompleted(),
      failed: await emailQueue.getFailed(),
    },
    analytics: {
      waiting: await analyticsQueue.getWaiting(),
      active: await analyticsQueue.getActive(),
      completed: await analyticsQueue.getCompleted(),
      failed: await analyticsQueue.getFailed(),
    },
    publishing: {
      waiting: await publishingQueue.getWaiting(),
      active: await publishingQueue.getActive(),
      completed: await publishingQueue.getCompleted(),
      failed: await publishingQueue.getFailed(),
    },
  };
  
  return stats;
}

// Graceful shutdown
export async function closeQueues(): Promise<void> {
  logger.info('Closing queues...');
  
  await Promise.all([
    videoGenerationQueue.close(),
    emailQueue.close(),
    analyticsQueue.close(),
    publishingQueue.close(),
  ]);
  
  await redis.disconnect();
  
  logger.info('All queues closed');
}

// Health check
export async function healthCheck(): Promise<boolean> {
  try {
    await redis.ping();
    return true;
  } catch (error) {
    logger.error('Queue health check failed:', error);
    return false;
  }
}
