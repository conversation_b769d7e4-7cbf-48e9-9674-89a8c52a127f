'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import {
  PlayIcon,
  EyeIcon,
  HeartIcon,
  ShareIcon,
  PencilIcon,
  TrashIcon,
  CloudArrowUpIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import { formatDistanceToNow } from 'date-fns';

interface Video {
  id: string;
  title: string;
  description?: string;
  thumbnailUrl?: string;
  fileUrl?: string;
  duration: number;
  status: 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'PUBLISHED';
  published: boolean;
  platforms: string[];
  createdAt: string;
  analytics?: {
    views: number;
    likes: number;
    shares: number;
  };
}

interface VideoCardProps {
  video: Video;
  onPlay: (video: Video) => void;
  onEdit: (video: Video) => void;
  onDelete: (video: Video) => void;
  onPublish: (video: Video) => void;
}

export function VideoCard({ video, onPlay, onEdit, onDelete, onPublish }: VideoCardProps) {
  const [isLiked, setIsLiked] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const getStatusIcon = () => {
    switch (video.status) {
      case 'PROCESSING':
        return <ClockIcon className="w-4 h-4 text-yellow-500" />;
      case 'COMPLETED':
        return <CheckCircleIcon className="w-4 h-4 text-green-500" />;
      case 'FAILED':
        return <ExclamationCircleIcon className="w-4 h-4 text-red-500" />;
      case 'PUBLISHED':
        return <CloudArrowUpIcon className="w-4 h-4 text-blue-500" />;
      default:
        return null;
    }
  };

  const getStatusText = () => {
    switch (video.status) {
      case 'PROCESSING':
        return 'Processing...';
      case 'COMPLETED':
        return 'Ready';
      case 'FAILED':
        return 'Failed';
      case 'PUBLISHED':
        return 'Published';
      default:
        return 'Unknown';
    }
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -4 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-300 hover:shadow-lg"
    >
      {/* Thumbnail */}
      <div className="relative aspect-[9/16] bg-gray-100 dark:bg-gray-700 overflow-hidden">
        {video.thumbnailUrl ? (
          <Image
            src={video.thumbnailUrl}
            alt={video.title}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-900 dark:to-primary-800">
            <PlayIcon className="w-12 h-12 text-primary-600 dark:text-primary-400" />
          </div>
        )}

        {/* Duration badge */}
        <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
          {formatDuration(video.duration)}
        </div>

        {/* Status badge */}
        <div className="absolute top-2 left-2 flex items-center space-x-1 bg-white/90 dark:bg-gray-800/90 text-xs px-2 py-1 rounded-full">
          {getStatusIcon()}
          <span className="font-medium">{getStatusText()}</span>
        </div>

        {/* Play overlay */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: isHovered && video.status === 'COMPLETED' ? 1 : 0 }}
          className="absolute inset-0 bg-black/50 flex items-center justify-center cursor-pointer"
          onClick={() => video.status === 'COMPLETED' && onPlay(video)}
        >
          <motion.div
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="bg-white/20 backdrop-blur-sm rounded-full p-4"
          >
            <PlayIcon className="w-8 h-8 text-white" />
          </motion.div>
        </motion.div>

        {/* Platform badges */}
        {video.platforms.length > 0 && (
          <div className="absolute top-2 right-2 flex space-x-1">
            {video.platforms.slice(0, 3).map((platform) => (
              <div
                key={platform}
                className="bg-white/90 dark:bg-gray-800/90 text-xs px-1.5 py-0.5 rounded text-gray-700 dark:text-gray-300"
              >
                {platform.charAt(0).toUpperCase()}
              </div>
            ))}
            {video.platforms.length > 3 && (
              <div className="bg-white/90 dark:bg-gray-800/90 text-xs px-1.5 py-0.5 rounded text-gray-700 dark:text-gray-300">
                +{video.platforms.length - 3}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Content */}
      <div className="p-4">
        {/* Title */}
        <h3 className="font-semibold text-gray-900 dark:text-white text-sm line-clamp-2 mb-2">
          {video.title}
        </h3>

        {/* Description */}
        {video.description && (
          <p className="text-gray-600 dark:text-gray-400 text-xs line-clamp-2 mb-3">
            {video.description}
          </p>
        )}

        {/* Analytics */}
        {video.analytics && (
          <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400 mb-3">
            <div className="flex items-center space-x-1">
              <EyeIcon className="w-3 h-3" />
              <span>{formatNumber(video.analytics.views)}</span>
            </div>
            <div className="flex items-center space-x-1">
              <HeartIcon className="w-3 h-3" />
              <span>{formatNumber(video.analytics.likes)}</span>
            </div>
            <div className="flex items-center space-x-1">
              <ShareIcon className="w-3 h-3" />
              <span>{formatNumber(video.analytics.shares)}</span>
            </div>
          </div>
        )}

        {/* Timestamp */}
        <p className="text-xs text-gray-500 dark:text-gray-400 mb-3">
          {formatDistanceToNow(new Date(video.createdAt), { addSuffix: true })}
        </p>

        {/* Actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {/* Like button */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setIsLiked(!isLiked)}
              className="p-1.5 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              {isLiked ? (
                <HeartSolidIcon className="w-4 h-4 text-red-500" />
              ) : (
                <HeartIcon className="w-4 h-4 text-gray-400" />
              )}
            </motion.button>

            {/* Share button */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className="p-1.5 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <ShareIcon className="w-4 h-4 text-gray-400" />
            </motion.button>
          </div>

          <div className="flex items-center space-x-1">
            {/* Edit button */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => onEdit(video)}
              className="p-1.5 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <PencilIcon className="w-4 h-4 text-gray-400" />
            </motion.button>

            {/* Publish button */}
            {!video.published && video.status === 'COMPLETED' && (
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => onPublish(video)}
                className="p-1.5 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900 transition-colors"
              >
                <CloudArrowUpIcon className="w-4 h-4 text-blue-500" />
              </motion.button>
            )}

            {/* Delete button */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => onDelete(video)}
              className="p-1.5 rounded-full hover:bg-red-100 dark:hover:bg-red-900 transition-colors"
            >
              <TrashIcon className="w-4 h-4 text-red-500" />
            </motion.button>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
