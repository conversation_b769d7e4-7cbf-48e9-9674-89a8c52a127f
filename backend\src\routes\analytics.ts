import express from 'express';
import { query, param, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { authenticateToken } from '../middleware/auth';
import { rateLimiter } from '../middleware/rateLimiter';
import { AnalyticsService } from '../services/analytics';
import { logger } from '../utils/logger';

const router = express.Router();
const prisma = new PrismaClient();

// Get dashboard analytics
router.get('/dashboard',
  authenticateToken,
  rateLimiter('general'),
  async (req, res) => {
    try {
      const stats = await AnalyticsService.getDashboardStats(req.user.id);
      res.json(stats);

    } catch (error) {
      logger.error('Error fetching dashboard analytics:', error);
      res.status(500).json({ message: 'Failed to fetch dashboard analytics' });
    }
  }
);

// Get video analytics
router.get('/videos/:videoId',
  authenticateToken,
  rateLimiter('general'),
  [
    param('videoId').isUUID(),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const { videoId } = req.params;

      // Verify video ownership
      const video = await prisma.video.findFirst({
        where: {
          id: videoId,
          userId: req.user.id,
        },
      });

      if (!video) {
        return res.status(404).json({ message: 'Video not found' });
      }

      const analytics = await AnalyticsService.getVideoAnalytics(videoId, req.user.id);
      res.json(analytics);

    } catch (error) {
      logger.error('Error fetching video analytics:', error);
      res.status(500).json({ message: 'Failed to fetch video analytics' });
    }
  }
);

// Get engagement trends
router.get('/trends',
  authenticateToken,
  rateLimiter('general'),
  [
    query('days').optional().isInt({ min: 1, max: 365 }),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const days = parseInt(req.query.days as string) || 30;
      const trends = await AnalyticsService.getEngagementTrends(req.user.id, days);
      
      res.json({
        period: `${days} days`,
        trends,
      });

    } catch (error) {
      logger.error('Error fetching engagement trends:', error);
      res.status(500).json({ message: 'Failed to fetch engagement trends' });
    }
  }
);

// Get platform performance comparison
router.get('/platforms',
  authenticateToken,
  rateLimiter('general'),
  async (req, res) => {
    try {
      const performance = await AnalyticsService.getPlatformPerformance(req.user.id);
      res.json(performance);

    } catch (error) {
      logger.error('Error fetching platform performance:', error);
      res.status(500).json({ message: 'Failed to fetch platform performance' });
    }
  }
);

// Track custom event
router.post('/events',
  authenticateToken,
  rateLimiter('general'),
  [
    query('event').isLength({ min: 1, max: 100 }),
    query('data').optional().isObject(),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const { event, data = {} } = req.body;

      await AnalyticsService.trackEvent({
        userId: req.user.id,
        event,
        data,
        sessionId: req.sessionID,
        userAgent: req.get('User-Agent'),
        ipAddress: req.ip,
      });

      res.json({ message: 'Event tracked successfully' });

    } catch (error) {
      logger.error('Error tracking event:', error);
      res.status(500).json({ message: 'Failed to track event' });
    }
  }
);

// Sync platform analytics
router.post('/sync',
  authenticateToken,
  rateLimiter('general'),
  async (req, res) => {
    try {
      await AnalyticsService.syncPlatformAnalytics(req.user.id);
      
      res.json({ 
        message: 'Analytics sync initiated. This may take a few minutes to complete.' 
      });

    } catch (error) {
      logger.error('Error initiating analytics sync:', error);
      res.status(500).json({ message: 'Failed to initiate analytics sync' });
    }
  }
);

// Get user activity timeline
router.get('/activity',
  authenticateToken,
  rateLimiter('general'),
  [
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('offset').optional().isInt({ min: 0 }),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const limit = parseInt(req.query.limit as string) || 20;
      const offset = parseInt(req.query.offset as string) || 0;

      const activities = await prisma.analyticsEvent.findMany({
        where: { userId: req.user.id },
        orderBy: { timestamp: 'desc' },
        take: limit,
        skip: offset,
        select: {
          id: true,
          event: true,
          data: true,
          timestamp: true,
        },
      });

      const total = await prisma.analyticsEvent.count({
        where: { userId: req.user.id },
      });

      res.json({
        activities: activities.map(activity => ({
          ...activity,
          description: formatEventDescription(activity.event, activity.data),
        })),
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total,
        },
      });

    } catch (error) {
      logger.error('Error fetching user activity:', error);
      res.status(500).json({ message: 'Failed to fetch user activity' });
    }
  }
);

// Get analytics summary for a date range
router.get('/summary',
  authenticateToken,
  rateLimiter('general'),
  [
    query('startDate').isISO8601(),
    query('endDate').isISO8601(),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const { startDate, endDate } = req.query;

      const [
        videoCount,
        totalViews,
        totalEngagement,
        platformBreakdown,
      ] = await Promise.all([
        // Video count in date range
        prisma.video.count({
          where: {
            userId: req.user.id,
            createdAt: {
              gte: new Date(startDate as string),
              lte: new Date(endDate as string),
            },
          },
        }),

        // Total views
        prisma.videoAnalytics.aggregate({
          where: {
            video: { userId: req.user.id },
            updatedAt: {
              gte: new Date(startDate as string),
              lte: new Date(endDate as string),
            },
          },
          _sum: { views: true },
        }),

        // Total engagement
        prisma.videoAnalytics.aggregate({
          where: {
            video: { userId: req.user.id },
            updatedAt: {
              gte: new Date(startDate as string),
              lte: new Date(endDate as string),
            },
          },
          _sum: {
            likes: true,
            shares: true,
            comments: true,
          },
        }),

        // Platform breakdown
        prisma.videoAnalytics.groupBy({
          by: ['platform'],
          where: {
            video: { userId: req.user.id },
            updatedAt: {
              gte: new Date(startDate as string),
              lte: new Date(endDate as string),
            },
          },
          _sum: {
            views: true,
            likes: true,
            shares: true,
            comments: true,
          },
        }),
      ]);

      const summary = {
        period: {
          startDate,
          endDate,
        },
        metrics: {
          videosCreated: videoCount,
          totalViews: totalViews._sum.views || 0,
          totalLikes: totalEngagement._sum.likes || 0,
          totalShares: totalEngagement._sum.shares || 0,
          totalComments: totalEngagement._sum.comments || 0,
          totalEngagement: 
            (totalEngagement._sum.likes || 0) +
            (totalEngagement._sum.shares || 0) +
            (totalEngagement._sum.comments || 0),
        },
        platforms: platformBreakdown.reduce((acc, platform) => {
          acc[platform.platform] = {
            views: platform._sum.views || 0,
            likes: platform._sum.likes || 0,
            shares: platform._sum.shares || 0,
            comments: platform._sum.comments || 0,
            engagement: 
              (platform._sum.likes || 0) +
              (platform._sum.shares || 0) +
              (platform._sum.comments || 0),
          };
          return acc;
        }, {} as Record<string, any>),
      };

      res.json(summary);

    } catch (error) {
      logger.error('Error fetching analytics summary:', error);
      res.status(500).json({ message: 'Failed to fetch analytics summary' });
    }
  }
);

// Export analytics data
router.get('/export',
  authenticateToken,
  rateLimiter('general'),
  [
    query('format').optional().isIn(['json', 'csv']),
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601(),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const format = req.query.format as string || 'json';
      const startDate = req.query.startDate as string;
      const endDate = req.query.endDate as string;

      const whereClause: any = { userId: req.user.id };
      
      if (startDate && endDate) {
        whereClause.createdAt = {
          gte: new Date(startDate),
          lte: new Date(endDate),
        };
      }

      const videos = await prisma.video.findMany({
        where: whereClause,
        include: {
          analytics: true,
        },
        orderBy: { createdAt: 'desc' },
      });

      const exportData = videos.map(video => ({
        videoId: video.id,
        title: video.title,
        createdAt: video.createdAt,
        status: video.status,
        platforms: video.platforms,
        analytics: video.analytics.map(analytics => ({
          platform: analytics.platform,
          views: analytics.views,
          likes: analytics.likes,
          shares: analytics.shares,
          comments: analytics.comments,
          engagementRate: analytics.views > 0 
            ? ((analytics.likes + analytics.shares + analytics.comments) / analytics.views * 100).toFixed(2)
            : '0.00',
        })),
      }));

      if (format === 'csv') {
        // Convert to CSV format
        const csvData = convertToCSV(exportData);
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename=analytics-export.csv');
        res.send(csvData);
      } else {
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', 'attachment; filename=analytics-export.json');
        res.json(exportData);
      }

    } catch (error) {
      logger.error('Error exporting analytics:', error);
      res.status(500).json({ message: 'Failed to export analytics' });
    }
  }
);

// Helper functions
function formatEventDescription(event: string, data: any): string {
  switch (event) {
    case 'video_generated':
      return `Video "${data.title || 'Untitled'}" generated successfully`;
    case 'video_published':
      return `Video published to ${data.platforms?.join(', ') || 'platforms'}`;
    case 'subscription_upgraded':
      return `Subscription upgraded to ${data.tierId}`;
    case 'api_call':
      return `API call made to ${data.endpoint}`;
    case 'login':
      return 'User logged in';
    case 'logout':
      return 'User logged out';
    default:
      return `${event.replace(/_/g, ' ')} event occurred`;
  }
}

function convertToCSV(data: any[]): string {
  if (data.length === 0) return '';

  const headers = [
    'Video ID',
    'Title',
    'Created At',
    'Status',
    'Platforms',
    'Platform',
    'Views',
    'Likes',
    'Shares',
    'Comments',
    'Engagement Rate (%)',
  ];

  const rows = [];
  rows.push(headers.join(','));

  data.forEach(video => {
    if (video.analytics.length === 0) {
      rows.push([
        video.videoId,
        `"${video.title}"`,
        video.createdAt,
        video.status,
        `"${video.platforms.join(', ')}"`,
        '',
        '0',
        '0',
        '0',
        '0',
        '0.00',
      ].join(','));
    } else {
      video.analytics.forEach((analytics: any) => {
        rows.push([
          video.videoId,
          `"${video.title}"`,
          video.createdAt,
          video.status,
          `"${video.platforms.join(', ')}"`,
          analytics.platform,
          analytics.views,
          analytics.likes,
          analytics.shares,
          analytics.comments,
          analytics.engagementRate,
        ].join(','));
      });
    }
  });

  return rows.join('\n');
}

export default router;
