{"version": 2, "name": "viralab", "builds": [{"src": "frontend/package.json", "use": "@vercel/next", "config": {"distDir": ".next"}}, {"src": "backend/src/index.ts", "use": "@vercel/node", "config": {"includeFiles": ["backend/prisma/**"]}}], "routes": [{"src": "/api/(.*)", "dest": "/backend/src/index.ts"}, {"src": "/(.*)", "dest": "/frontend/$1"}], "env": {"NODE_ENV": "production"}, "build": {"env": {"NODE_ENV": "production", "NEXT_TELEMETRY_DISABLED": "1"}}, "functions": {"backend/src/index.ts": {"maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}, {"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}], "rewrites": [{"source": "/api/:path*", "destination": "/backend/src/index.ts"}], "redirects": [{"source": "/home", "destination": "/", "permanent": true}], "regions": ["iad1", "sfo1", "lhr1"], "github": {"enabled": false}}