import rateLimit from 'express-rate-limit';
import slowDown from 'express-slow-down';
import { Request, Response } from 'express';
import { createClient } from 'redis';
import { logger } from '../utils/logger';

// Redis client for rate limiting
const redisClient = createClient({
  url: process.env.REDIS_URL,
});

redisClient.on('error', (err) => {
  logger.error('Redis Rate Limiter Error:', err);
});

// Custom store for Redis-based rate limiting
class RedisStore {
  private client: any;
  private prefix: string;

  constructor(client: any, prefix = 'rl:') {
    this.client = client;
    this.prefix = prefix;
  }

  async incr(key: string): Promise<{ totalHits: number; resetTime: Date }> {
    const redisKey = `${this.prefix}${key}`;
    const windowMs = 15 * 60 * 1000; // 15 minutes
    const now = Date.now();
    const window = Math.floor(now / windowMs);
    const windowKey = `${redisKey}:${window}`;

    try {
      const multi = this.client.multi();
      multi.incr(windowKey);
      multi.expire(window<PERSON><PERSON>, Math.ceil(windowMs / 1000));
      const results = await multi.exec();
      
      const totalHits = results[0][1];
      const resetTime = new Date(window * windowMs + windowMs);
      
      return { totalHits, resetTime };
    } catch (error) {
      logger.error('Redis rate limit error:', error);
      // Fallback to allowing the request if Redis fails
      return { totalHits: 1, resetTime: new Date(Date.now() + windowMs) };
    }
  }

  async decrement(key: string): Promise<void> {
    const redisKey = `${this.prefix}${key}`;
    const windowMs = 15 * 60 * 1000;
    const now = Date.now();
    const window = Math.floor(now / windowMs);
    const windowKey = `${redisKey}:${window}`;

    try {
      await this.client.decr(windowKey);
    } catch (error) {
      logger.error('Redis rate limit decrement error:', error);
    }
  }

  async resetKey(key: string): Promise<void> {
    const redisKey = `${this.prefix}${key}`;
    try {
      const keys = await this.client.keys(`${redisKey}:*`);
      if (keys.length > 0) {
        await this.client.del(keys);
      }
    } catch (error) {
      logger.error('Redis rate limit reset error:', error);
    }
  }
}

// Initialize Redis store
const store = new RedisStore(redisClient);

// Subscription tier limits
const TIER_LIMITS = {
  FREE: { requests: 10, videos: 5 },
  STARTER: { requests: 100, videos: 100 },
  PRO: { requests: 1000, videos: -1 }, // Unlimited
  AGENCY: { requests: 5000, videos: -1 },
  ENTERPRISE: { requests: 10000, videos: -1 },
};

// Key generator function
const keyGenerator = (req: Request): string => {
  const userId = req.user?.id;
  const ip = req.ip || req.connection.remoteAddress;
  
  // Use user ID if authenticated, otherwise use IP
  return userId ? `user:${userId}` : `ip:${ip}`;
};

// Skip function for successful requests (optional)
const skipSuccessfulRequests = (req: Request, res: Response): boolean => {
  return res.statusCode < 400;
};

// Custom handler for rate limit exceeded
const rateLimitHandler = (req: Request, res: Response) => {
  const retryAfter = Math.round(req.rateLimit?.resetTime?.getTime() - Date.now()) / 1000;
  
  logger.warn(`Rate limit exceeded for ${keyGenerator(req)}`, {
    ip: req.ip,
    userId: req.user?.id,
    path: req.path,
    method: req.method,
  });

  res.status(429).json({
    error: 'Too Many Requests',
    message: 'Rate limit exceeded. Please try again later.',
    retryAfter: Math.max(retryAfter, 0),
    limit: req.rateLimit?.limit,
    remaining: req.rateLimit?.remaining,
    resetTime: req.rateLimit?.resetTime,
  });
};

// General API rate limiter
export const rateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: (req: Request) => {
    const user = req.user;
    if (!user) return 50; // Anonymous users
    
    const tier = user.subscriptionTier || 'FREE';
    return TIER_LIMITS[tier as keyof typeof TIER_LIMITS]?.requests || 10;
  },
  keyGenerator,
  skipSuccessfulRequests: process.env.RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS === 'true',
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
  // store: store, // Uncomment when Redis is properly connected
});

// Video generation rate limiter (more restrictive)
export const videoRateLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: (req: Request) => {
    const user = req.user;
    if (!user) return 1; // Anonymous users get 1 per hour
    
    const tier = user.subscriptionTier || 'FREE';
    const limit = TIER_LIMITS[tier as keyof typeof TIER_LIMITS]?.videos;
    return limit === -1 ? 1000 : limit; // Use 1000 as "unlimited" for rate limiting
  },
  keyGenerator: (req: Request) => `video:${keyGenerator(req)}`,
  handler: (req: Request, res: Response) => {
    logger.warn(`Video generation rate limit exceeded for ${keyGenerator(req)}`);
    
    res.status(429).json({
      error: 'Video Generation Limit Exceeded',
      message: 'You have reached your video generation limit. Please upgrade your plan or wait before creating more videos.',
      upgradeUrl: '/pricing',
      currentTier: req.user?.subscriptionTier || 'FREE',
    });
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Authentication rate limiter (for login attempts)
export const authRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  keyGenerator: (req: Request) => {
    const email = req.body?.email;
    const ip = req.ip || req.connection.remoteAddress;
    return email ? `auth:${email}` : `auth:ip:${ip}`;
  },
  handler: (req: Request, res: Response) => {
    logger.warn(`Authentication rate limit exceeded`, {
      email: req.body?.email,
      ip: req.ip,
    });
    
    res.status(429).json({
      error: 'Too Many Login Attempts',
      message: 'Too many failed login attempts. Please try again in 15 minutes.',
      retryAfter: 15 * 60, // 15 minutes in seconds
    });
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// API key rate limiter (for API endpoints)
export const apiKeyRateLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: (req: Request) => {
    const apiKey = req.headers['x-api-key'] as string;
    // You would look up the API key's tier in the database
    // For now, return a default limit
    return 1000;
  },
  keyGenerator: (req: Request) => {
    const apiKey = req.headers['x-api-key'] as string;
    return `api:${apiKey}`;
  },
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
});

// Slow down middleware for gradual response delays
export const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 50, // Allow 50 requests per windowMs without delay
  delayMs: 500, // Add 500ms delay per request after delayAfter
  maxDelayMs: 20000, // Maximum delay of 20 seconds
  keyGenerator,
});

// Export Redis client for cleanup
export { redisClient as rateLimitRedis };
