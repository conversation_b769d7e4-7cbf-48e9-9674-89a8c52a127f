import winston from 'winston';
import path from 'path';

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Tell winston that you want to link the colors
winston.addColors(colors);

// Define which level to log based on environment
const level = () => {
  const env = process.env.NODE_ENV || 'development';
  const isDevelopment = env === 'development';
  return isDevelopment ? 'debug' : 'warn';
};

// Define different log formats
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`,
  ),
);

const fileLogFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
);

// Define transports
const transports = [
  // Console transport
  new winston.transports.Console({
    level: level(),
    format: logFormat,
  }),
  
  // File transport for errors
  new winston.transports.File({
    filename: path.join(process.cwd(), 'logs', 'error.log'),
    level: 'error',
    format: fileLogFormat,
    maxsize: 5242880, // 5MB
    maxFiles: 5,
  }),
  
  // File transport for all logs
  new winston.transports.File({
    filename: path.join(process.cwd(), 'logs', 'combined.log'),
    format: fileLogFormat,
    maxsize: 5242880, // 5MB
    maxFiles: 5,
  }),
];

// Add Sentry transport in production
if (process.env.NODE_ENV === 'production' && process.env.SENTRY_DSN) {
  const Sentry = require('@sentry/node');
  const SentryTransport = require('winston-sentry-log');
  
  transports.push(
    new SentryTransport({
      sentry: Sentry,
      level: 'error',
    })
  );
}

// Create the logger
export const logger = winston.createLogger({
  level: level(),
  levels,
  format: fileLogFormat,
  transports,
  exitOnError: false,
});

// Create a stream object for Morgan HTTP logging
export const loggerStream = {
  write: (message: string) => {
    logger.http(message.trim());
  },
};

// Enhanced logging functions with context
export const logWithContext = {
  error: (message: string, context?: any, error?: Error) => {
    logger.error(message, {
      context,
      stack: error?.stack,
      timestamp: new Date().toISOString(),
    });
  },

  warn: (message: string, context?: any) => {
    logger.warn(message, {
      context,
      timestamp: new Date().toISOString(),
    });
  },

  info: (message: string, context?: any) => {
    logger.info(message, {
      context,
      timestamp: new Date().toISOString(),
    });
  },

  debug: (message: string, context?: any) => {
    logger.debug(message, {
      context,
      timestamp: new Date().toISOString(),
    });
  },

  http: (message: string, context?: any) => {
    logger.http(message, {
      context,
      timestamp: new Date().toISOString(),
    });
  },
};

// Performance logging
export const performanceLogger = {
  start: (operation: string) => {
    const startTime = Date.now();
    return {
      end: (context?: any) => {
        const duration = Date.now() - startTime;
        logger.info(`Performance: ${operation} completed in ${duration}ms`, {
          operation,
          duration,
          context,
          timestamp: new Date().toISOString(),
        });
      },
    };
  },
};

// API request logging
export const apiLogger = {
  request: (req: any, res: any, next: any) => {
    const startTime = Date.now();
    
    res.on('finish', () => {
      const duration = Date.now() - startTime;
      const logData = {
        method: req.method,
        url: req.url,
        statusCode: res.statusCode,
        duration,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        userId: req.user?.id,
        timestamp: new Date().toISOString(),
      };

      if (res.statusCode >= 400) {
        logger.error(`API Error: ${req.method} ${req.url}`, logData);
      } else {
        logger.http(`API Request: ${req.method} ${req.url}`, logData);
      }
    });

    next();
  },
};

// Database query logging
export const dbLogger = {
  query: (query: string, params?: any[], duration?: number) => {
    logger.debug('Database Query', {
      query,
      params,
      duration,
      timestamp: new Date().toISOString(),
    });
  },

  error: (query: string, error: Error, params?: any[]) => {
    logger.error('Database Error', {
      query,
      params,
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
    });
  },
};

// Security logging
export const securityLogger = {
  authFailure: (email: string, ip: string, reason: string) => {
    logger.warn('Authentication Failure', {
      email,
      ip,
      reason,
      timestamp: new Date().toISOString(),
    });
  },

  suspiciousActivity: (userId: string, activity: string, context?: any) => {
    logger.warn('Suspicious Activity', {
      userId,
      activity,
      context,
      timestamp: new Date().toISOString(),
    });
  },

  rateLimitExceeded: (ip: string, endpoint: string, limit: number) => {
    logger.warn('Rate Limit Exceeded', {
      ip,
      endpoint,
      limit,
      timestamp: new Date().toISOString(),
    });
  },
};

// Business logic logging
export const businessLogger = {
  videoGenerated: (userId: string, videoId: string, config: any) => {
    logger.info('Video Generated', {
      userId,
      videoId,
      config,
      timestamp: new Date().toISOString(),
    });
  },

  subscriptionChanged: (userId: string, oldTier: string, newTier: string) => {
    logger.info('Subscription Changed', {
      userId,
      oldTier,
      newTier,
      timestamp: new Date().toISOString(),
    });
  },

  paymentProcessed: (userId: string, amount: number, currency: string, status: string) => {
    logger.info('Payment Processed', {
      userId,
      amount,
      currency,
      status,
      timestamp: new Date().toISOString(),
    });
  },

  apiUsage: (userId: string, endpoint: string, cost: number) => {
    logger.info('API Usage', {
      userId,
      endpoint,
      cost,
      timestamp: new Date().toISOString(),
    });
  },
};

// Error handling for uncaught exceptions
process.on('uncaughtException', (error: Error) => {
  logger.error('Uncaught Exception', {
    error: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString(),
  });
  process.exit(1);
});

process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
  logger.error('Unhandled Rejection', {
    reason: reason?.message || reason,
    stack: reason?.stack,
    promise: promise.toString(),
    timestamp: new Date().toISOString(),
  });
});

// Graceful shutdown logging
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
});

export default logger;
