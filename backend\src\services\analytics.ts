import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import { addToQueue } from './queue';

const prisma = new PrismaClient();

export interface AnalyticsEvent {
  userId: string;
  event: string;
  data: Record<string, any>;
  timestamp?: Date;
  sessionId?: string;
  userAgent?: string;
  ipAddress?: string;
  country?: string;
  city?: string;
}

export interface DashboardStats {
  totalVideos: number;
  totalViews: number;
  totalLikes: number;
  totalShares: number;
  monthlyUsage: number;
  usageLimit: number;
  topPerformingVideos: Array<{
    id: string;
    title: string;
    views: number;
    engagement: number;
  }>;
  platformBreakdown: Record<string, number>;
  recentActivity: Array<{
    type: string;
    description: string;
    timestamp: Date;
  }>;
}

export interface VideoAnalytics {
  videoId: string;
  platform: string;
  views: number;
  likes: number;
  shares: number;
  comments: number;
  clickThroughRate: number;
  engagementRate: number;
  watchTime: number;
  demographics: {
    ageGroups: Record<string, number>;
    genders: Record<string, number>;
    countries: Record<string, number>;
  };
  performance: {
    impressions: number;
    reach: number;
    saves: number;
    profileVisits: number;
  };
}

export class AnalyticsService {
  // Track user event
  static async trackEvent(event: AnalyticsEvent): Promise<void> {
    try {
      await prisma.analyticsEvent.create({
        data: {
          userId: event.userId,
          event: event.event,
          data: event.data || {},
          timestamp: event.timestamp || new Date(),
          sessionId: event.sessionId,
          userAgent: event.userAgent,
          ipAddress: event.ipAddress,
          country: event.country,
          city: event.city,
        },
      });

      logger.info(`Analytics event tracked: ${event.event}`, {
        userId: event.userId,
        event: event.event,
      });

    } catch (error) {
      logger.error('Error tracking analytics event:', error);
      // Don't throw error to avoid breaking main functionality
    }
  }

  // Get dashboard statistics
  static async getDashboardStats(userId: string): Promise<DashboardStats> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          videos: {
            include: {
              analytics: true,
            },
          },
        },
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Calculate current month usage
      const currentMonth = new Date();
      currentMonth.setDate(1);
      currentMonth.setHours(0, 0, 0, 0);

      const monthlyUsage = await prisma.video.count({
        where: {
          userId,
          createdAt: { gte: currentMonth },
        },
      });

      // Get subscription limits
      const subscriptionTiers: Record<string, number> = {
        FREE: 5,
        STARTER: 50,
        PRO: 200,
        AGENCY: 1000,
        ENTERPRISE: -1,
      };

      const usageLimit = subscriptionTiers[user.subscriptionTier] || 5;

      // Aggregate video analytics
      const totalVideos = user.videos.length;
      let totalViews = 0;
      let totalLikes = 0;
      let totalShares = 0;
      const platformBreakdown: Record<string, number> = {};

      user.videos.forEach(video => {
        video.analytics.forEach(analytics => {
          totalViews += analytics.views;
          totalLikes += analytics.likes;
          totalShares += analytics.shares;
          
          platformBreakdown[analytics.platform] = 
            (platformBreakdown[analytics.platform] || 0) + analytics.views;
        });
      });

      // Get top performing videos
      const topPerformingVideos = user.videos
        .map(video => {
          const totalVideoViews = video.analytics.reduce((sum, a) => sum + a.views, 0);
          const totalVideoEngagement = video.analytics.reduce((sum, a) => sum + a.likes + a.shares + a.comments, 0);
          
          return {
            id: video.id,
            title: video.title,
            views: totalVideoViews,
            engagement: totalVideoEngagement,
          };
        })
        .sort((a, b) => b.views - a.views)
        .slice(0, 5);

      // Get recent activity
      const recentActivity = await this.getRecentActivity(userId, 10);

      return {
        totalVideos,
        totalViews,
        totalLikes,
        totalShares,
        monthlyUsage,
        usageLimit,
        topPerformingVideos,
        platformBreakdown,
        recentActivity,
      };

    } catch (error) {
      logger.error('Error getting dashboard stats:', error);
      throw new Error('Failed to fetch dashboard statistics');
    }
  }

  // Get video analytics
  static async getVideoAnalytics(videoId: string, userId: string): Promise<VideoAnalytics[]> {
    try {
      const video = await prisma.video.findFirst({
        where: { id: videoId, userId },
        include: {
          analytics: true,
        },
      });

      if (!video) {
        throw new Error('Video not found');
      }

      return video.analytics.map(analytics => ({
        videoId: analytics.videoId,
        platform: analytics.platform,
        views: analytics.views,
        likes: analytics.likes,
        shares: analytics.shares,
        comments: analytics.comments,
        clickThroughRate: analytics.clickThroughRate || 0,
        engagementRate: this.calculateEngagementRate(analytics),
        watchTime: analytics.watchTime || 0,
        demographics: analytics.demographics as any || {
          ageGroups: {},
          genders: {},
          countries: {},
        },
        performance: analytics.performance as any || {
          impressions: 0,
          reach: 0,
          saves: 0,
          profileVisits: 0,
        },
      }));

    } catch (error) {
      logger.error('Error getting video analytics:', error);
      throw new Error('Failed to fetch video analytics');
    }
  }

  // Update video analytics from platform APIs
  static async updateVideoAnalytics(
    videoId: string,
    platform: string,
    analyticsData: Partial<VideoAnalytics>
  ): Promise<void> {
    try {
      await prisma.videoAnalytics.upsert({
        where: {
          videoId_platform: {
            videoId,
            platform,
          },
        },
        update: {
          views: analyticsData.views || 0,
          likes: analyticsData.likes || 0,
          shares: analyticsData.shares || 0,
          comments: analyticsData.comments || 0,
          clickThroughRate: analyticsData.clickThroughRate || 0,
          watchTime: analyticsData.watchTime || 0,
          demographics: analyticsData.demographics || {},
          performance: analyticsData.performance || {},
          updatedAt: new Date(),
        },
        create: {
          videoId,
          platform,
          views: analyticsData.views || 0,
          likes: analyticsData.likes || 0,
          shares: analyticsData.shares || 0,
          comments: analyticsData.comments || 0,
          clickThroughRate: analyticsData.clickThroughRate || 0,
          watchTime: analyticsData.watchTime || 0,
          demographics: analyticsData.demographics || {},
          performance: analyticsData.performance || {},
        },
      });

      logger.info(`Analytics updated for video ${videoId} on ${platform}`);

    } catch (error) {
      logger.error('Error updating video analytics:', error);
      throw error;
    }
  }

  // Get user engagement trends
  static async getEngagementTrends(
    userId: string,
    days: number = 30
  ): Promise<Array<{ date: string; views: number; engagement: number }>> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const analytics = await prisma.videoAnalytics.findMany({
        where: {
          video: { userId },
          updatedAt: { gte: startDate },
        },
        include: {
          video: true,
        },
        orderBy: { updatedAt: 'asc' },
      });

      // Group by date
      const trendData: Record<string, { views: number; engagement: number }> = {};

      analytics.forEach(record => {
        const date = record.updatedAt.toISOString().split('T')[0];
        if (!trendData[date]) {
          trendData[date] = { views: 0, engagement: 0 };
        }
        
        trendData[date].views += record.views;
        trendData[date].engagement += record.likes + record.shares + record.comments;
      });

      return Object.entries(trendData).map(([date, data]) => ({
        date,
        views: data.views,
        engagement: data.engagement,
      }));

    } catch (error) {
      logger.error('Error getting engagement trends:', error);
      throw new Error('Failed to fetch engagement trends');
    }
  }

  // Get platform performance comparison
  static async getPlatformPerformance(userId: string): Promise<Record<string, {
    videos: number;
    totalViews: number;
    avgEngagement: number;
    topVideo: { title: string; views: number } | null;
  }>> {
    try {
      const videos = await prisma.video.findMany({
        where: { userId },
        include: {
          analytics: true,
        },
      });

      const platformStats: Record<string, any> = {};

      videos.forEach(video => {
        video.analytics.forEach(analytics => {
          const platform = analytics.platform;
          
          if (!platformStats[platform]) {
            platformStats[platform] = {
              videos: new Set(),
              totalViews: 0,
              totalEngagement: 0,
              topVideo: null,
            };
          }

          platformStats[platform].videos.add(video.id);
          platformStats[platform].totalViews += analytics.views;
          platformStats[platform].totalEngagement += 
            analytics.likes + analytics.shares + analytics.comments;

          if (!platformStats[platform].topVideo || 
              analytics.views > platformStats[platform].topVideo.views) {
            platformStats[platform].topVideo = {
              title: video.title,
              views: analytics.views,
            };
          }
        });
      });

      // Convert to final format
      const result: Record<string, any> = {};
      Object.entries(platformStats).forEach(([platform, stats]: [string, any]) => {
        result[platform] = {
          videos: stats.videos.size,
          totalViews: stats.totalViews,
          avgEngagement: stats.videos.size > 0 ? stats.totalEngagement / stats.videos.size : 0,
          topVideo: stats.topVideo,
        };
      });

      return result;

    } catch (error) {
      logger.error('Error getting platform performance:', error);
      throw new Error('Failed to fetch platform performance');
    }
  }

  // Helper methods
  private static calculateEngagementRate(analytics: any): number {
    if (analytics.views === 0) return 0;
    const totalEngagement = analytics.likes + analytics.shares + analytics.comments;
    return (totalEngagement / analytics.views) * 100;
  }

  private static async getRecentActivity(userId: string, limit: number): Promise<Array<{
    type: string;
    description: string;
    timestamp: Date;
  }>> {
    try {
      const events = await prisma.analyticsEvent.findMany({
        where: { userId },
        orderBy: { timestamp: 'desc' },
        take: limit,
      });

      return events.map(event => ({
        type: event.event,
        description: this.formatEventDescription(event.event, event.data),
        timestamp: event.timestamp,
      }));

    } catch (error) {
      logger.error('Error getting recent activity:', error);
      return [];
    }
  }

  private static formatEventDescription(event: string, data: any): string {
    switch (event) {
      case 'video_generated':
        return `Video "${data.title || 'Untitled'}" generated successfully`;
      case 'video_published':
        return `Video published to ${data.platforms?.join(', ') || 'platforms'}`;
      case 'subscription_upgraded':
        return `Subscription upgraded to ${data.tierId}`;
      case 'api_call':
        return `API call made to ${data.endpoint}`;
      default:
        return `${event.replace(/_/g, ' ')} event occurred`;
    }
  }

  // Batch update analytics from external APIs
  static async syncPlatformAnalytics(userId: string): Promise<void> {
    try {
      const videos = await prisma.video.findMany({
        where: { userId, published: true },
        select: { id: true, platforms: true },
      });

      for (const video of videos) {
        for (const platform of video.platforms) {
          // Add job to queue for each platform sync
          await addToQueue('analytics', 'sync-platform-analytics', {
            videoId: video.id,
            platform,
            userId,
          });
        }
      }

      logger.info(`Queued analytics sync for ${videos.length} videos`);

    } catch (error) {
      logger.error('Error syncing platform analytics:', error);
      throw error;
    }
  }
}
