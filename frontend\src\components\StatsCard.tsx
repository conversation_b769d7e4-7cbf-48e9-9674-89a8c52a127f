'use client';

import { motion } from 'framer-motion';
import { 
  TrendingUpIcon, 
  TrendingDownIcon, 
  EyeIcon, 
  HeartIcon, 
  ShareIcon, 
  ChatBubbleLeftIcon,
  PlayIcon,
  UsersIcon,
  CurrencyDollarIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

interface StatsCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
    period: string;
  };
  icon?: 'views' | 'likes' | 'shares' | 'comments' | 'videos' | 'users' | 'revenue' | 'time';
  loading?: boolean;
  className?: string;
}

const iconMap = {
  views: EyeIcon,
  likes: HeartIcon,
  shares: ShareIcon,
  comments: ChatBubbleLeftIcon,
  videos: PlayIcon,
  users: UsersIcon,
  revenue: CurrencyDollarIcon,
  time: ClockIcon,
};

const formatValue = (value: string | number): string => {
  if (typeof value === 'number') {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toLocaleString();
  }
  return value;
};

export default function StatsCard({
  title,
  value,
  change,
  icon = 'views',
  loading = false,
  className = '',
}: StatsCardProps) {
  const IconComponent = iconMap[icon];

  if (loading) {
    return (
      <div className={`bg-white rounded-xl shadow-sm border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="flex items-center justify-between mb-4">
            <div className="h-4 bg-gray-200 rounded w-24"></div>
            <div className="h-8 w-8 bg-gray-200 rounded-lg"></div>
          </div>
          <div className="h-8 bg-gray-200 rounded w-20 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-32"></div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow ${className}`}
    >
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-gray-600">{title}</h3>
        <div className="p-2 bg-primary-50 rounded-lg">
          <IconComponent className="h-5 w-5 text-primary-600" />
        </div>
      </div>

      <div className="mb-2">
        <p className="text-2xl font-bold text-gray-900">
          {formatValue(value)}
        </p>
      </div>

      {change && (
        <div className="flex items-center">
          {change.type === 'increase' ? (
            <TrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
          ) : (
            <TrendingDownIcon className="h-4 w-4 text-red-500 mr-1" />
          )}
          <span
            className={`text-sm font-medium ${
              change.type === 'increase' ? 'text-green-600' : 'text-red-600'
            }`}
          >
            {change.value > 0 ? '+' : ''}{change.value}%
          </span>
          <span className="text-sm text-gray-500 ml-1">
            vs {change.period}
          </span>
        </div>
      )}
    </motion.div>
  );
}

// Specialized stats cards for different metrics
export function ViewsCard({ views, change, loading }: {
  views: number;
  change?: StatsCardProps['change'];
  loading?: boolean;
}) {
  return (
    <StatsCard
      title="Total Views"
      value={views}
      change={change}
      icon="views"
      loading={loading}
    />
  );
}

export function LikesCard({ likes, change, loading }: {
  likes: number;
  change?: StatsCardProps['change'];
  loading?: boolean;
}) {
  return (
    <StatsCard
      title="Total Likes"
      value={likes}
      change={change}
      icon="likes"
      loading={loading}
    />
  );
}

export function SharesCard({ shares, change, loading }: {
  shares: number;
  change?: StatsCardProps['change'];
  loading?: boolean;
}) {
  return (
    <StatsCard
      title="Total Shares"
      value={shares}
      change={change}
      icon="shares"
      loading={loading}
    />
  );
}

export function VideosCard({ videos, change, loading }: {
  videos: number;
  change?: StatsCardProps['change'];
  loading?: boolean;
}) {
  return (
    <StatsCard
      title="Videos Created"
      value={videos}
      change={change}
      icon="videos"
      loading={loading}
    />
  );
}

export function EngagementCard({ rate, change, loading }: {
  rate: number;
  change?: StatsCardProps['change'];
  loading?: boolean;
}) {
  return (
    <StatsCard
      title="Engagement Rate"
      value={`${rate.toFixed(1)}%`}
      change={change}
      icon="likes"
      loading={loading}
    />
  );
}

export function RevenueCard({ revenue, change, loading }: {
  revenue: number;
  change?: StatsCardProps['change'];
  loading?: boolean;
}) {
  return (
    <StatsCard
      title="Revenue"
      value={`$${revenue.toLocaleString()}`}
      change={change}
      icon="revenue"
      loading={loading}
    />
  );
}

// Usage stats card for subscription limits
export function UsageCard({ 
  current, 
  limit, 
  title = "Monthly Usage",
  loading = false 
}: {
  current: number;
  limit: number;
  title?: string;
  loading?: boolean;
}) {
  const percentage = limit > 0 ? (current / limit) * 100 : 0;
  const isNearLimit = percentage >= 80;
  const isOverLimit = percentage >= 100;

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-24 mb-4"></div>
          <div className="h-8 bg-gray-200 rounded w-20 mb-4"></div>
          <div className="h-2 bg-gray-200 rounded w-full mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-32"></div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
    >
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-gray-600">{title}</h3>
        <div className="p-2 bg-primary-50 rounded-lg">
          <PlayIcon className="h-5 w-5 text-primary-600" />
        </div>
      </div>

      <div className="mb-4">
        <p className="text-2xl font-bold text-gray-900">
          {current.toLocaleString()}
          <span className="text-sm font-normal text-gray-500 ml-1">
            / {limit === -1 ? '∞' : limit.toLocaleString()}
          </span>
        </p>
      </div>

      {limit > 0 && (
        <>
          <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${
                isOverLimit
                  ? 'bg-red-500'
                  : isNearLimit
                  ? 'bg-yellow-500'
                  : 'bg-primary-500'
              }`}
              style={{ width: `${Math.min(percentage, 100)}%` }}
            />
          </div>
          <p className={`text-sm ${
            isOverLimit
              ? 'text-red-600'
              : isNearLimit
              ? 'text-yellow-600'
              : 'text-gray-500'
          }`}>
            {percentage.toFixed(1)}% used
            {isOverLimit && ' (Over limit!)'}
            {isNearLimit && !isOverLimit && ' (Near limit)'}
          </p>
        </>
      )}

      {limit === -1 && (
        <p className="text-sm text-green-600 font-medium">
          Unlimited usage
        </p>
      )}
    </motion.div>
  );
}
